I have a service. 
create a Docker image from current directory with following requirements
Use base image nvidia/cuda:12.4.0-devel-ubuntu22.04
Install ubuntu tools like curl, bash, telnet  etc. to use for debugging, checking etc. 
Install follwoing along with conda and python 3.12

RUN apt-get update && apt-get install -y --no-install-recommends \
    wget \
    curl \
    git \
    ca-certificates \
    build-essential \
    libssl-dev \
    libffi-dev \
    openssh-server \
    && rm -rf /var/lib/apt/lists/* \
    && wget --quiet https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh \
    && bash /tmp/miniconda.sh -b -p /opt/conda \
    && rm /tmp/miniconda.sh \
    && conda update -n base -c defaults conda -y

# Set Python version and install PyTorch
RUN conda install -y python=3.12      && conda clean -afy

create a use comfy 
create /ComfyUI folder and assign ownership to comfy user

Change user to comfy

copy all the files and folders recursively to /ComfyUI folder  
except input, output, model, custom_nodes extra_nodes
create empty folders for input, output, model, custom_nodes


copy folders listed in fixed_nodes.txt to custom_nodes folder
Install torch with  following command
RUN bash -c "\
    pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124 "

Then pip install requirements.txt from base folder

Now iterate through the custom nodes folder and pip install any requirements.txt files except for those folders listed in fixed_nodes_no_requirements.txt

Now change directory to /ComfyUI
Expose port 18892 18893 

run entrypoint.sh



Create an entrypoint.sh with following
-----------------------------------
If no argument is given then get into container with bash 

arguments will 
runtype additional_install_behavior

runtype will be 
    full 
    debug

additional_install_behavior will be
    none
    main_req
    all_req

In both cases execute following 

conda activate qs

if additional_install_behavior is 
main_req then 
    pip install -r extra_nodes_requirements.txt
all_req then
    Then go through each directory under custom_nodes provided in extra_nodes.txt file and 
    pip install -r requirements.txt  (from each directory)
None then
    dont install anything
if runtype is debug then
    python main.py --multi-user  --disable-all-custom-nodes --listen 0.0.0.0 --port 18893--use-pytorch-cross-attention   2>&1 > /tmp/comfy_debug.log 

if runtype is full then
    python main.py --multi-user --listen 0.0.0.0 --port 18892 --use-pytorch-cross-attention 2>&1 > /tmp/comfy_full.log

----------------------------------

Create docker-compose.yml with following 
For all this first is host directory and second is container directory
mount volumes /qsfs2/services/ComfyUI/models to /ComfyUI/models 
volume /qsfs2/services/ComfyUI/input to /ComfyUI/input
volume /qsfs2/services/ComfyUI/output to /ComfyUI/output
Now go through each line in extra_nodes.txt file and create a volume (if looping in yml is not possible create pre script)
volume /qsfs2/services/ComfyUI/custom_nodes/xxx to /ComfyUI/custom_nodes/xxx

create target for full and debug  based on argument pass the argument to entrypoint.sh

Create docker file, entrypoint.sh docker-compose.yml


Write a script that takes 2 arguments 
1. Directory to be mounted from  if not present default value is ~/qs/repos/qsComfy/qs_custom_nodes
2. name of node directory inside directory of argument 1, if argument 2 is not present then all the node directories to be mounted

First unmount all the fuse mounts that are mounted inside 
./custom_nodes  directory
Then bindfs mount fully writable node directories from directory given by argument 1 and argument 2 
inside ./custom_nodes directory 

----------------------------
I have an existing conda environment qs 
I want to create and use new environment qs2,  In qs2 want to install only additional packages not present in qs. and in runtime use both envs to import packages.  How to achive this






