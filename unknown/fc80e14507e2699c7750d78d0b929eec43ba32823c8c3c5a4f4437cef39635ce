accelerate==1.2.1
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
albucore==0.0.16
albumentations==1.4.15
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.8.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.4
attrs==24.3.0
av==14.0.1
babel==2.16.0
beautifulsoup4==4.12.3
bleach==6.2.0
cachetools==5.5.0
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cmake==3.31.2
coloredlogs==15.0.1
colour-science==0.4.6
comm==0.2.2
contourpy==1.3.1
cryptography==44.0.0
git+https://github.com/WASasquatch/cstr@0520c29a18a7a869a6e5983861d6f7a4c86f8e9b
cycler==0.12.1
debugpy==1.8.11
decorator==5.1.1
deepdiff==8.1.1
defusedxml==0.7.1
Deprecated==1.2.15
diffusers==0.32.1
dill==0.3.9
distro==1.9.0
easydict==1.13
einops==0.8.0
eval_type_backport==0.2.2
executing==2.1.0
fairscale==0.4.13
fastjsonschema==2.21.1
git+https://github.com/WASasquatch/ffmpy.git@f000737698b387ffaeab7cd871b0e9185811230d
filelock==3.16.1
flatbuffers==24.12.23
flet==0.25.2
fonttools==4.55.3
fqdn==1.5.1
frozenlist==1.5.0
fsspec==2024.12.0
gdown==5.2.0
gitdb==4.0.12
GitPython==3.1.44
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.25.2
humanfriendly==10.0
idna==3.10
imageio==2.36.1
imageio-ffmpeg==0.5.1
git+https://github.com/WASasquatch/img2texture.git@d6159abea44a0b2cf77454d3d46962c8b21eb9d3
importlib_metadata==8.5.0
iopath==0.1.10
ipykernel==6.29.5
ipython==8.31.0
ipywidgets==8.1.5
isoduration==20.11.0
jedi==0.19.2
Jinja2==3.1.5
jiter==0.8.2
joblib==1.4.2
json5==0.10.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.11.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
jupyterlab==4.3.4
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
kiwisolver==1.4.8
kornia==0.7.4
kornia_rs==0.1.8
lazy_loader==0.4
lightning-utilities==0.11.9
llvmlite==0.43.0
git+https://github.com/Lightricks/LTX-Video@ea6d5d64894d617ec16b7d70a44e85b8499b3225
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.0
matplotlib-inline==0.1.7
matrix-client==0.4.0
mdurl==0.1.2
mistune==3.1.0
mpmath==1.3.0
multidict==6.1.0
nbclient==0.10.2
nbconvert==7.16.5
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
notebook==7.3.2
notebook_shim==0.2.4
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==9.1.0.70
nvidia-cufft-cu12==11.2.1.3
nvidia-curand-cu12==10.3.5.147
nvidia-cusolver-cu12==11.6.1.9
nvidia-cusparse-cu12==12.3.1.170
nvidia-ml-py==12.560.30
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
oauthlib==3.2.2
omegaconf==2.3.0
onnxruntime-gpu==1.20.1
openai==1.59.3
opencv-python==4.10.0.84
opencv-python-headless==4.10.0.84
orderly-set==5.2.3
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
peft==0.14.0
pexpect==4.9.0
piexif==1.1.3
pilgram==1.2.1
pillow==11.1.0
pixeloe==0.0.10
platformdirs==4.3.6
pooch==1.8.2
portalocker==3.1.1
prometheus_client==0.21.1
prompt_toolkit==3.0.48
propcache==0.2.1
protobuf==5.29.2
psutil==6.1.1
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pycparser==2.22
pydantic==2.10.4
pydantic_core==2.27.2
PyGithub==2.5.0
Pygments==2.19.0
PyJWT==2.10.1
PyMatting==1.1.13
PyNaCl==1.5.0
pynvml==12.0.0
pyparsing==3.2.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-json-logger==3.2.1
pytorch-lightning==2.5.0.post0
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
qrcode==8.0
referencing==0.35.1
regex==2024.11.6
rembg==2.0.61
repath==0.9.0
requests==2.32.3
requirements-parser==0.11.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.9.4
rich-argparse==1.6.0
rpds-py==0.22.3
safetensors==0.5.0
scikit-image==0.25.0
scikit-learn==1.6.0
scipy==1.15.0
segment-anything==1.0
Send2Trash==1.8.3
sentencepiece==0.2.0
setuptools==75.1.0
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soundfile==0.13.0
soupsieve==2.6
spandrel==0.4.0
stack-data==0.6.3
sympy==1.13.1
terminado==0.18.1
threadpoolctl==3.5.0
tifffile==2024.12.12
timm==1.0.12
tinycss2==1.4.0
tokenizers==0.21.0
torch==2.5.1+cu124
torchaudio==2.5.1+cu124
torchmetrics==1.6.1
torchsde==0.2.6
torchvision==0.20.1+cu124
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
trampoline==0.1.2
transformers==4.47.1
transparent-background==1.3.3
triton==3.1.0
typer==0.15.1
types-python-dateutil==2.9.0.20241206
types-setuptools==75.6.0.20241223
typing_extensions==4.12.2
tzdata==2024.2
uri-template==1.3.0
urllib3==1.26.20
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
wget==3.2
wheel==0.44.0
widgetsnbextension==4.0.13
wrapt==1.17.0
xformers==0.0.29.post1
yarl==1.18.3
zipp==3.21.0
