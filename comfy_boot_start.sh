#!/bin/bash
echo "comfy start at boot after safe mounting custom_nodes"
base_dir=$(dirname "$0")
echo "running in $base_dir"
$base_dir/qs_safe_mount.sh $base_dir/qs_custom_nodes $base_dir/custom_nodes ALL
cd $base_dir
./mount_other_dirs.sh
#now start comfy
echo "Starting 1: comfy client mode  from $base_dir  Logs will come to  /tmp/comfy_boot_28890.out base port 28890 nginx port 18890" 
eval "$(conda shell.bash hook)" && conda deactivate  && conda activate qs
nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
               --use-pytorch-cross-attention --port 28890 --run-mode client 2>&1 >/tmp/comfy_28890.out &
disown

echo "starting 2: comfy api server from $base_dir  Logs will come to /tmp/comfy_boot_28891.out base port 28891 nginx port 18891" 
# nohup $base_dir/start.sh 28893 AD  2>&1 > /tmp/comfy_boot_28893.out &
nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
               --use-pytorch-cross-attention --port 28891 --run-mode api  2>&1 >/tmp/comfy_28891.out & 
disown

echo "starting developer comfy server from $base_dir  Logs will come to /tmp/comfy_boot_28893.out base port 28893 nginx port 18893" 
nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
               --use-pytorch-cross-attention --port 28892 2>&1 >/tmp/comfy_28892.out &
disown
echo "comfy started 3 processes"

