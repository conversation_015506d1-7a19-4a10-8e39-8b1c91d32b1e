#!/bin/bash
PORT=28888

echo "Starting Jupyter to be authenticated via AD on port {$PORT}"
existing=`ps -aef | grep python | grep NotebookApp | grep $PORT`
#if existing is not empty then process is running so warn to stop it first
if [[ "${existing}" ]]; then
    echo "Process arleady running stop it first"
    echo ${existing}
    exit 1
fi

echo "starting process"
cd /qsfs2/services/infra/jupyter/
eval "$(conda shell.bash hook)" && conda activate jupyter 
echo "Starting Jupyter to be authenticated via AD"

nohup jupyter notebook --NotebookApp.token '' --NotebookApp.allow_origin='*'  --no-browser \
    --NotebookApp.tornado_settings='{"headers": {"Access-Control-Allow-Origin": "*"}}' \
    --ServerApp.base_url=/jupyter --ServerApp.trust_xheaders=True --ServerApp.allow_credentials=True \
    --ServerApp.allow_remote_access=True --FileContentsManager.checkpoints_kwargs="root_dir"="/qsfs2/services/.jupyter_checkpoints"\
    --port=$PORT --ip=0.0.0.0 --notebook-dir=/qsfs2 2>&1 > /tmp/jupyter_$PORT.out &

jupyterpid=$!

echo "started with pid " ${jupyterpid} 
disown
echo "started jupyter output at /tmp/jupyter_$PORT.out"
