#!/bin/bash
# This scrit is to test if check if newer version frozen_reqs.txt is working
# 1st parameter is given take it as requirements file 2nd parameter is given take it as env_name

frozen_reqs="${1:-./frozen_jupyter_requirements.txt}"
env_name="${2:-jupyter}"
echo running with $frozen_reqs and $env_name 
source ~/.bashrc
#check if frozen_reqs file exists
if [ ! -f "$frozen_reqs" ]; then
    echo "$frozen_reqs file not found"
    exit 1
fi

if [ -d "/home/<USER>/.conda/envs/$env_name" ] ; then
    echo "$env_name environment exist, do you want to delete and continue? (yes/no)"
    conda rename -n jupyter   jupyter.bak
fi
# delete current existing envs
conda env remove -n $env_name -y

#deactivate any current envs
eval "$(conda shell.bash hook)" && conda deactivate 2>/tmp/null; conda deactivate 2>/tmp/null; conda deactivate 2>/tmp/null 

conda create --name ${env_name} python=3.12.2 -y && eval "$(conda shell.bash hook)" && unset PYTHONPATH && conda activate ${env_name}

pip install torch==2.5.1+cu124 torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124
pip install torch==2.5.1+cu124 -U  xformers --index-url https://download.pytorch.org/whl/cu124
pip install -r ${frozen_reqs}

# check if torch is working 
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"
if [ $? -eq 0 ]; then 
    echo "torch working with new requirements"
else
    echo "torch not working with new requirements, do you want to delete test envs? (yes/no)"
fi
conda deactivate ; conda deactivate 


