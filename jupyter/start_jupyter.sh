#!/bin/bash
#The script takes 2 arguments 1st type: AD, SSL, <PERSON>SSL 2nd port number
if [ -z "$1" ]
then
	echo "No argument supplied "
	echo "Usage ./start.sh [type: AD, SSL, NSSL]  [port] "
	TYPE=AD
else
	TYPE=$1
fi

if [ -z "$2" ]
then
	echo "No argument supplied using port 28888 by default"
	PORT=28888
else
	PORT=$2
fi

echo "Starting Jupyter to be authenticated via {$TYPE} on port {$PORT}"
existing=`ps -aef | grep python | grep NotebookApp | grep $PORT`

if [[ -z "${existing}" ]]; then
	echo "starting process"
	cd /qsfs2/services/infra/jupyter/
	eval "$(conda shell.bash hook)" && conda activate jupyter 
	if [[ $TYPE == "AD" ]]; then
		echo "Starting Jupyter to be authenticated via AD"
		nohup jupyter notebook --NotebookApp.token '' --NotebookApp.allow_origin='*' \
			--NotebookApp.tornado_settings='{"headers": {"Access-Control-Allow-Origin": "*"}}' \
			--ServerApp.base_url=/jupyter --ServerApp.trust_xheaders=True --ServerApp.allow_credentials=True \
			--ServerApp.allow_remote_access=True --FileContentsManager.checkpoints_kwargs="root_dir"="/qsfs2/services/.jupyter_checkpoints" --no-browser \
			--port=$PORT --ip=0.0.0.0 --notebook-dir=/qsfs2 2>&1 > /tmp/jupyter_$PORT.out &
		jupyterpid=$!
		echo "started with pid " ${jupyterpid} 
		disown
	elif [[ $TYPE == "SSL" ]]; then
		nohup jupyter notebook --NotebookApp.token 'QsJupyter@123' \
			--keyfile /etc/letsencrypt/qstools/certs/privkey.pem \
			--certfile /etc/letsencrypt/qstools/certs/cert.pem \
			--FileContentsManager.checkpoints_kwargs="root_dir"="/qsfs2/services/.jupyter_checkpoints" --no-browser \
			--port=$PORT --ip=0.0.0.0 --notebook-dir=/qsfs2 2>&1 > /tmp/jupyter_$PORT.out &
		jupyterpid=$!
		echo "started with pid " ${jupyterpid} 
		disown
	elif [[ $TYPE == "NSSL" ]]; then
		nohup jupyter notebook --NotebookApp.token 'QsJupyter@123' \
			--FileContentsManager.checkpoints_kwargs="root_dir"="/qsfs2/services/.jupyter_checkpoints" --no-browser \
			--port=$PORT --ip=0.0.0.0 --notebook-dir=/qsfs2 2>&1 > /tmp/jupyter_$PORT.out &
		jupyterpid=$!
		echo "started with pid " ${jupyterpid} 
		disown
	else
		echo "Invalid type of authentication not starting"
		exit 1
	fi
	echo ${jupyterpid} > /tmp/jupyter_${PORT}.pid

else
	echo "Process arleady running stop it first"
	echo ${existing}
	exit 1
fi

