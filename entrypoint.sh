#!/bin/bash

eval "$(conda shell.bash hook)" && conda deactivate && conda activate qs
    conda env list
for port in 28890 28891 28892; do
  case $port in
    28890) run_mode="client" ;;
    28891) run_mode="api" ;;
    28892) run_mode="development" ;;
  esac
  log_file="/tmp/log_${port}.txt"
  error_file="/tmp/error_${port}.txt"
  python main.py --port $port --run-mode $run_mode ${QSCOMFY_ARGS} > ${log_file} 2> ${log_file} &
done

# Prevent the entrypoint.sh script from exiting
tail -f /dev/null