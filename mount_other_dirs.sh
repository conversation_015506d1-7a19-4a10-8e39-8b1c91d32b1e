#!/bin/bash
# Default values
DEFAULT_SOURCE_DIR="/qsfs2/services/qsComfy/"
DEFAULT_TARGET_DIR="./"

# Read arguments
SOURCE_DIR="${1:-$DEFAULT_SOURCE_DIR}"
TARGET_DIR="${2:-$DEFAULT_TARGET_DIR}"

echo "This is non safe mount if something already exists will try to unmount and remount or use qs_safe_mount.sh"
echo "Usage: $0 [source_dir] [target_dir]"

#iterate over input outpu as dtypes
for dtyps in input output models my_workflows user
do
    echo "Mounting $dtyps directory..."
    source_dir="$SOURCE_DIR/$dtyps/"
    target_dir="$TARGET_DIR/$dtyps/"
    echo "mounting $source_dir to $target_dir"
    # Check if the source is a directory
    if [ -d "$source_dir" ]; then
        # Ensure the target directory exists
        mkdir -p "$target_dir"
        #check if the target directory is  mounted already if so unmount it
        grep "fuse" /proc/mounts | awk -v target="$target_dir" '$2 ~ "^"target {print $2}' | while read -r mount_point; do
            sudo fusermount -u "$mount_point"
            echo "unmounted"
        done
        # Mount using bindfs
        echo "Mounting $source_dir to $target_dir..."
        sudo bindfs --perms=a+w "$source_dir" "$target_dir"
    fi
done

echo "Operation completed!"
