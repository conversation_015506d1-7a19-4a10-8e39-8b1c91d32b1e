version: '3'
services:
  qscomfy:
    image: qsacr001.azurecr.io/qs-comfy:latest
    environment:
      - QSCOMFY_ARGS=--use-pytorch-cross-attention --enable-cors-header --multi-user
    volumes:
      - /qsfs2/services/qsComfy/custom_nodes:/qsComfy/custom_nodes
      - /qsfs2/services/qsComfy/models:/qsComfy/models
      - /qsfs2/services/qsComfy/input:/qsComfy/input
      - /qsfs2/services/qsComfy/output:/qsComfy/output
      - /qsfs2/services/qsComfy/artlet:/qsComfy/artlet
      - /qsfs2/services/qsComfy/qs_custom_nodes:/qsComfy/qs_custom_nodes
   
    logging:
      driver: "json-file"
      options:
        labels: "com.example.logging"
        env: "os,customer"
      
    network_mode: "host"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
              
