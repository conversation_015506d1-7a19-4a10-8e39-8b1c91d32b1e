from PIL import Image
import tifffile as tiff
import random
import os
import numpy as np
import shutil
import cv2
from PIL import Image
import torch
import ffmpeg
import imageio
import comfy 
from torch.nn import functional as F
from comfy.k_diffusion.utils import FolderOfImages


# class QSVideoCombine: 
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(s):
#         return {
#             "required": {
#               "input_video": ("STRING", {"placeholder": "X://insert/path/here.png", "vhs_path_extensions": list(FolderOfImages.IMG_EXTENSIONS)}),
#               "input_2_images": ("IMAGE",),
#                 "output_dir": ("STRING",{"default": "./output/"}),
#                 "output_video": ("STRING",{"default": 'output'}),
#             },
#         }

#     RETURN_TYPES = ("STRING",)
#     RETURN_NAMES = ("output_video_path",)
#     CATEGORY = "Artlet/QS"
#     OUTPUT_NODE = True
#     FUNCTION = "qs_video_combine"



#     def qs_video_combine(self, input_video, input_2_images, output_dir='./output/', output_video='output'):
#         """
#         Combines a sequence of images (from a tensor) into a video file
#         while preserving the codec, format, and metadata from the input video.

#         Args:
#             input_video (str): Path to the input video file.
#             input_2_images (torch.Tensor): Video frames as a tensor of shape (frames, height, width, 3) with values in [0, 1].
#             output_dir (str, optional): Directory to save the output video. Defaults to './output/'.
#             output_video (str, optional): Name of the output video file. Defaults to 'output'.

#         Returns:
#             str: Path to the generated video file.
#         """
        
#         # Create a temp folder for storing frames
#         temp_folder = f'/tmp/qsv{random.randint(0, 999)}'
#         os.makedirs(temp_folder, exist_ok=True)
        
#         # Ensure output directory exists
#         os.makedirs(output_dir, exist_ok=True)
        
#         # Resolve full paths
#         output_dir = os.path.abspath(output_dir)
#         input_video = os.path.abspath(input_video)

#         print("input_video:", input_video, "\noutput_dir:", output_dir, "\ntemp_folder:", temp_folder)

#         if not os.path.exists(input_video):
#             raise ValueError(f"Input video '{input_video}' does not exist.")

#         # Extract metadata from the input video
#         try:
#             probe = ffmpeg.probe(input_video, cmd='ffprobe', v='error')
#         except Exception as e:
#             raise RuntimeError(f"Error probing input video '{input_video}': {e}")

#         video_stream = next((s for s in probe["streams"] if s["codec_type"] == "video"), None)
#         audio_stream = next((s for s in probe["streams"] if s["codec_type"] == "audio"), None)

#         if not video_stream:
#             raise ValueError("No video stream found in input video.")

#         probe = ffmpeg.probe(input_video)
#         video_stream = next(stream for stream in probe["streams"] if stream["codec_type"] == "video")
#         video_codec = video_stream["codec_name"]  # Should be 'prores'
#         pixel_format = video_stream.get("pix_fmt", "yuv422p10le")
#         framerate = video_stream["r_frame_rate"]  # Usually "25/1"
#         bitrate = video_stream.get("bit_rate", "178112k")  # Keep ProRes bitrate

#         # Extract video properties
#         container_format = probe["format"]["format_name"]  # e.g., 'mp4', 'mov'
#         bit_depth = int(video_stream.get("bits_per_raw_sample", 16))  # Default 8-bit

#         print(f"Detected codec: {video_codec}, pixel format: {pixel_format}, framerate: {framerate}, bitrate: {bitrate}, bit depth: {bit_depth}")

#         # Determine suitable image format
#         if bit_depth > 8:
#             image_format = "tif"
#             dtype = np.uint16 if bit_depth <= 16 else np.float32
#         else:
#             image_format = "png"
#             dtype = np.uint8

#         print(f"Saving frames as {image_format.upper()} ({dtype})...")

#         # Save frames to temp folder
#         frames, height, width, channels = input_2_images.shape
#         assert channels == 3, "Tensor must have 3 channels (RGB)."


#         # Determine output file format
#         input_ext = os.path.splitext(input_video)[-1].lower()

#         # Generate output video name
#         output_video = os.path.splitext(output_video)[0]
#         out_sr_no = 1
#         while True:
#             final_output_video = os.path.join(output_dir, f"{output_video}_{out_sr_no:03d}{input_ext}")
#             if not os.path.exists(final_output_video):
#                 break
#             out_sr_no += 1

#         print("Final output video path:", final_output_video)
#         for (batch_number, image) in enumerate(input_2_images):
#             try:
#                 i = (65535. * image.cpu().numpy()).astype(np.uint16)  # Scale to 16-bit range
#                 img = np.clip(i, 0, 65535).astype(np.uint16)
#                 file = f"frame_{batch_number:04d}.tif"
#                 imageio.imwrite(os.path.join(temp_folder, file), img)
#             except Exception as e:
#                 print(f"Error saving frame {file} {batch_number}: {e}")
                
#         # Encode video using the same settings as input
#         temp_video_path = os.path.join(temp_folder, "video" + input_ext)
#         ffmpeg.input(f"{temp_folder}/frame_%04d.tif", framerate=framerate) \
#             .output(temp_video_path, vcodec=video_codec, pix_fmt=pixel_format, bitrate=bitrate, r=framerate, preset="slow") \
#             .run(overwrite_output=True)

#         # Extract and merge audio if available
#         # Extract and merge audio if available
#         if audio_stream:
#             print("Extracting and adding original audio...")
#             audio_path = os.path.join(temp_folder, f"audio.wav")  # Convert to WAV for better compatibility
#             ffmpeg.input(input_video).output(audio_path, acodec="pcm_s16le").run(overwrite_output=True)

#             ffmpeg.output(
#                 ffmpeg.input(temp_video_path),
#                 ffmpeg.input(audio_path),
#                 final_output_video,
#                 vcodec=video_codec,  # Preserve original video codec
#                 pix_fmt=pixel_format,  # Preserve pixel format
#                 r=framerate,  # Match frame rate
#                **{"b:v": bitrate}, 
#                 acodec="copy"  # Keep original audio
#             ).run(overwrite_output=True)
         
#             os.remove(audio_path)  # Clean up temporary audio file
#         else:
#             os.rename(temp_video_path, final_output_video)
#         # Cleanup temp folder
#         shutil.rmtree(temp_folder, ignore_errors=True)
#         print(f"Done! Output saved as {final_output_video}")

#         return (final_output_video,0)

# class QSVideoCut:
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(cls):
#         return {
#             "required": {
#                 "input_filename": ("STRING",),
#                 "output_dir": ("STRING",{"default": "./output/"}),
#                 "output_video": ("STRING",{"default": 'output'}),
#                 "start": ("INT", {"default": 1}),
#                 "end": ("INT", {"default": None}),
#             },
#             "optional": {
#             }
#         }
#     RETURN_TYPES = ("STRING",)
#     RETURN_NAMES = ("output_video",)
#     FUNCTION = "cut_video"
#     CATEGORY = "QS"
#     OUTPUT_NODE = True
    
#     def cut_video(self, input_filename, output_dir, output_video, start, end):
#         if not os.path.exists(input_filename) or not os.path.isfile(input_filename):
#             raise ValueError(f"Input video '{input_filename}' does not exist or is not a file.")
#         output_video = os.path.splitext(output_video)[0]
#         input_ext = os.path.splitext(input_filename)[-1].lower()
#         output_filename = os.path.join(output_dir, f"{output_video}{input_ext}")

#         probe = ffmpeg.probe(input_filename)
        
#         # Extract video stream info
#         video_stream = next(stream for stream in probe["streams"] if stream["codec_type"] == "video")
#         video_codec = video_stream["codec_name"]
#         pixel_format = video_stream.get("pix_fmt", "yuv422p10le")
#         framerate = eval(video_stream["r_frame_rate"])  # Convert "num/den" to float
#         bitrate = video_stream.get("bit_rate", "178112k")

#         # Check for audio stream
#         audio_streams = [s for s in probe["streams"] if s["codec_type"] == "audio"]
#         has_audio = len(audio_streams) > 0

#         # Convert frame numbers to timestamps
#         start_time = (start - 1) / framerate
#         end_time = (end) / framerate

#         print(f"Cutting video from frame {start} to {end} (timestamps: {start_time:.3f}s - {end_time:.3f}s)")

#         # Generate a temporary video file
    
#         duration = end_time - start_time  
#         ffmpeg.input(input_filename, ss=start_time, t=duration) \
#             .output(output_filename, c="copy") \
#             .run(overwrite_output=True)
      
#         print(f"Video cut from frame {start} to {end} saved as {output_filename}")
#         return (output_filename,)
    
# class QSVideoConcat:
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(cls):
#         return {
#             "required": {
#                 "input_1": ("STRING",),
#                 "input_2": ("STRING",{"default": None}),
#                 "input_3": ("STRING",{"default": None}),
#                 "input_4": ("STRING",{"default": None}),
#                 "input_5": ("STRING",{"default": None}),
#                 "output_dir": ("STRING",{"default": "./output/"}),
#                 "output_video": ("STRING",{"default": 'output'}),
                
#             },
#             "optional": {
#                 "times_1":("STRING",{"default": ","}),
#                 "times_2":("STRING",{"default": ","}),
#                 "times_3":("STRING",{"default": ","}),
#                 "times_4":("STRING",{"default": ","}),
#                 "times_5":("STRING",{"default": ","}),
#             }
#         }
#     RETURN_TYPES = ("STRING",)
#     RETURN_NAMES = ("output_video",)
#     FUNCTION = "concat_video"
#     CATEGORY = "QS"
#     OUTPUT_NODE = True
    
#     def concat_video(self, input_1, input_2, input_3, input_4, input_5, output_dir, output_video,
#                      times_1 = ',', times_2 = ',', times_3 = ',', times_4 = ',', times_5 = ','):
#         input_files = [ f for f in [input_1, input_2, input_3, input_4, input_5]]
#         times = [ f for f in [times_1, times_2, times_3, times_4, times_5] ]
#         #skip 1 where there is no correspo
        
#         if len(input_files) < 2:
#             raise ValueError("No valid input files provided. minumum 2 required")
        
#         temp_folder = f'/tmp/qsvconcat_{random.randint(0, 999)}'
#         os.makedirs(temp_folder, exist_ok=True)
#         input_ext = os.path.splitext(input_files[0])[-1].lower()
#         output_filename = os.path.join(output_dir, f"{output_video}{input_ext}")
  
#         master_file = input_files[0]

#         # Get master file format, codec, and other parameters
#         probe = ffmpeg.probe(master_file)
#         format_info = probe["format"]
#         video_stream = next((s for s in probe["streams"] if s["codec_type"] == "video"), None)
#         audio_stream = next((s for s in probe["streams"] if s["codec_type"] == "audio"), None)

#         if not video_stream:
#             raise ValueError(f"No video stream found in {master_file}")

#         # Extract key format parameters
#         codec = video_stream["codec_name"]
#         width = video_stream["width"]
#         height = video_stream["height"]
#         bitrate = format_info.get("bit_rate", "1000000")
#         framerate = eval(video_stream["avg_frame_rate"]) if "avg_frame_rate" in video_stream else 30
#         pix_fmt = video_stream.get("pix_fmt", "yuv420p")
        
#         audio_codec = audio_stream["codec_name"] if audio_stream else "aac"
#         audio_bitrate = audio_stream.get("bit_rate", "128k") if audio_stream else "128k"
#         audio_channels = audio_stream.get("channels", 2) if audio_stream else 2
#         audio_sample_rate = audio_stream.get("sample_rate", "44100") if audio_stream else "44100"

#         converted_files = []
        
#         # Convert all files to match the master file format
#         for i, input_file in enumerate(input_files):
#             if input_file is None or os.path.exists(input_file) == False or  os.path.isfile(input_file) == False:
#                 print(f"Skipping {input_file} as it does not exist or is not a file")
#                 continue
#             time_to_use = times[i]
#             start_time = time_to_use.split(",")[0]
#             end_time = time_to_use.split(",")[1]
#             start_time = float(start_time) if start_time != '' else 0
#             end_time = float(end_time) if end_time != '' else 100000
#             converted_file = f"{temp_folder}/temp_{i}.mov"
#             (
#                 ffmpeg
#                 .input(input_file, ss=start_time, t=end_time)
#                 .output(
#                     converted_file,
#                     vcodec=codec,
#                     acodec=audio_codec,
#                     video_bitrate=bitrate,
#                     audio_bitrate=audio_bitrate,
#                     r=framerate,
#                     s=f"{width}x{height}",
#                     pix_fmt=pix_fmt,
#                     ar=audio_sample_rate,
#                     ac=audio_channels
#                 )
#                 .overwrite_output()
#                 .run()
#             )
#             converted_files.append(converted_file)

#         # Create a text file listing the converted files for ffmpeg concat
#         list_file = "concat_list.txt"
#         with open(list_file, "w") as f:
#             for file in converted_files:
#                 f.write(f"file '{file}'\n")

#         # Concatenate files
#         (
#             ffmpeg
#             .input(list_file, format="concat", safe=0)
#             .output(output_filename, c="copy")
#             .overwrite_output()
#             .run()
#         )

#         # Cleanup temp files
#         for file in converted_files:
#             os.remove(file)
#         os.remove(list_file)

#         print(f"Concatenated video saved as {output_filename}")
#         return (output_filename,)
      
# class QSConvertToMP4:
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(cls):
#         return {
#             "required": {
#                 "input_file": ("STRING",),
#                 "output_file": ("STRING",),
#             },
#             "optional": {
#             }
#         }
#     RETURN_TYPES = ("STRING",)
#     RETURN_NAMES = ("output_video",)
#     FUNCTION = "convert_to_mp4"
#     CATEGORY = "QS"
#     OUTPUT_NODE = True
    
#     def convert_to_mp4(self, input_file, output_file):
#         try:
#             probe = ffmpeg.probe(input_file)
#             video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
#             if video_stream:
#                 original_width = int(video_stream['width'])
#                 original_height = int(video_stream['height'])
#                 target_width = min(original_width, 1920)
#                 target_height = min(original_height, 1080)
#             else:
#                 target_width, target_height = 1920, 1080
            
#             (
#                 ffmpeg
#                 .input(input_file)
#                 .output(output_file, vcodec='libx264', acodec='aac', audio_bitrate='192k', ar='48000', strict='experimental', movflags='faststart',
#                         vf=f'scale={target_width}:{target_height}', preset='slow', pix_fmt='yuv420p', profile='baseline', level='3.0')
#                 .run(overwrite_output=True)
#             )
#             print(f"Conversion successful: {output_file}")
#         except ffmpeg.Error as e:
#             print("Error during conversion:", e)
        
#         return (output_file,)


# class QSLoadVideo:
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(cls):
#         return {
#             "required": {
#                 "input_file": ("STRING",),
#             },
#             "optional": {
#             }
#         }
#     RETURN_TYPES = ("IMAGE",)
#     RETURN_NAMES = ("output_video_images",)
#     FUNCTION = "qs_video_to_tensor"
#     CATEGORY = "QS"
#     OUTPUT_NODE = True
    
#     def qs_video_to_tensor(self, input_file):
#         """
#         Extracts frames from a video and converts them into a tensor while preserving precision and quality.
        
#         Args:
#             input_video (str): Path to the input video file.
        
#         Returns:
#             torch.Tensor: A tensor of shape (frames, height, width, 3) with values in [0, 1] and dtype float32.
#         """
        
#         if not os.path.exists(input_file):
#             raise ValueError(f"Input video '{input_file}' does not exist.")
        
#         # Probe video metadata
#         try:
#             probe = ffmpeg.probe(input_file)
#         except Exception as e:
#             raise RuntimeError(f"Error probing input video '{input_file}': {e}")
        
#         video_stream = next((s for s in probe["streams"] if s["codec_type"] == "video"), None)
#         if not video_stream:
#             raise ValueError("No video stream found in input video.")
        
#         width, height = int(video_stream['width']), int(video_stream['height'])
#         pixel_format = video_stream.get("pix_fmt", "yuv420p")
#         bit_depth = int(video_stream.get("bits_per_raw_sample", 8))
#         frame_rate = eval(video_stream.get("r_frame_rate", "30/1"))  # Convert "25/1" to float
        
#         # Determine dtype based on bit depth
#         if bit_depth > 8:
#             dtype = np.uint16  # TIFF can store 16-bit images
#         else:
#             dtype = np.uint8
#         temp_folder = os.path.join('/tmp/', f'qs_video_to_tensor_temp_{random.randint(0, 9999)}')
#         os.makedirs(temp_folder, exist_ok=True) 
        
#         frame_pattern = os.path.join(temp_folder, "frame_%04d.tif")
        
#         # Extract frames as images
#         ffmpeg.input(input_file).output(frame_pattern, format='image2', vcodec='tiff').run(overwrite_output=True)
        
#         # Load frames into a list
#         frame_files = sorted(f for f in os.listdir(temp_folder) if f.endswith(".tif"))
        
#         frames = []
#         for frame_file in frame_files:
#             img_path = os.path.join(temp_folder, frame_file)
#             img = cv2.imread(img_path, cv2.IMREAD_UNCHANGED)  # Load as is, preserving bit depth
#             img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

#             if img is None:
#                 raise RuntimeError(f"Failed to load image {img_path}")
            
#             # Normalize to [0, 1]
#             img = img.astype(np.float32) / np.iinfo(dtype).max
#             frames.append(img)
    
#         # Convert list to tensor
#         video_tensor = torch.tensor(np.stack(frames), dtype=torch.float32)
#         shutil.rmtree(temp_folder)
#         return (video_tensor,)

# class QSVideoZoom:
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(cls):
#         return {
#             "required": {
#                 "input_video": ("IMAGE",),
#                 "x1": ("INT",),
#                 "y1": ("INT",),
#                 "x2": ("INT",),
#                 "t1": ("INT",),
#                 "t2": ("INT",),
#                 "t3": ("INT",),
#                 "t4": ("INT",),
#             },
#             "optional": {
#             }
#         }
#     RETURN_TYPES = ("IMAGE",)
#     RETURN_NAMES = ("output_video_images",)
#     FUNCTION = "apply_zoom"
#     CATEGORY = "QS"
#     def calculate_y2(self, x1, y1, x2, height, width):
#         """Calculate y2 keeping aspect ratio same."""
#         aspect_ratio = height / width
#         new_height = (x2 - x1) * aspect_ratio
#         y2 = int(y1 + new_height)
#         zoom_factor = (x2 - x1)/width
#         return (min(y2, height), zoom_factor)  # Ensure it doesn't exceed frame height

#     def apply_zoom(self, input_video, x1, y1, x2, t1, t2, t3, t4):
#         """
#         Apply zoom transition to a video tensor.

#         Args:
#         - input_video: Tensor of shape (frames, height, width, channels)
#         - x1, y1: Top-left corner of zoomed area
#         - x2: Bottom-right x-coordinate (y2 is computed)
#         - t1, t2, t3, t4: Frames defining zoom timing
#         - device: 'cuda' for GPU, 'cpu' otherwise
#         """
#         device = comfy.model_management.get_torch_device()
#         input_video = input_video.to(device)
#         frames, height, width, channels = input_video.shape
#         (y2, zoom_factor) = self.calculate_y2(x1, y1, x2, height, width)
        
#         zoomed_video = input_video.clone()
        
#         for t in range(frames):
#             #alpha values will be between 0 to 1 if 0 no zoom no crop, if 1 full zoom full crop
#             if t < t1:  # No zoom
#                 continue
#             # if t < (t2 - t1 / 2):  # Zooming in
#             #     continue  # to test for 50% zoom
#             if t1 <= t < t2:  # Zooming in
#                 alpha = (t - t1) / (t2 - t1)
#             elif t2 <= t < t3:  # Fully zoomed
#                 alpha = 1.0
#             elif t3 <= t < t4:  # Zooming out
#                 alpha = 1 - ((t - t3) / (t4 - t3))
#             else:  # No zoom
#                 continue

#             # Compute zoomed crop
#             # crop_x1 = int(x1 + (width - x2) * (1 - alpha) / 2)
#             # crop_y1 = int(y1 + (height - y2) * (1 - alpha) / 2)
#             # crop_x2 = int(x2 - (width - x2) * (1 - alpha) / 2)
#             # crop_y2 = int(y2 - (height - y2) * (1 - alpha) / 2)
#             crop_x1 = 0 + int(alpha * x1)
#             crop_y1 = 0 + int(alpha * y1)
#             crop_x2 = x2 + int((1. - alpha) * (width - x2))
#             crop_y2 = y2 + int((1. - alpha) * (height - y2))
#             crop_x1, crop_y1 = max(0, crop_x1), max(0, crop_y1)
#             crop_x2, crop_y2 = min(width, crop_x2), min(height, crop_y2)

#             # Extract zoomed region and resize
#             cropped_frame = input_video[t, crop_y1:crop_y2, crop_x1:crop_x2, :]

#             # Upscale to original size
#             zoomed_frame = F.interpolate(
#                 cropped_frame.permute(2, 0, 1).unsqueeze(0), 
#                 size=(height, width), 
#                 mode="bilinear", 
#                 align_corners=False
#             ).squeeze(0).permute(1, 2, 0)

#             zoomed_video[t] = zoomed_frame

#         return (zoomed_video.cpu(),)



NODE_CLASS_MAPPINGS = {
    # "QSVideoCombine": QSVideoCombine,
    # "QSVideoCut": QSVideoCut,
    # "QSVideoConcat": QSVideoConcat,
    # "QSConvertToMP4":QSConvertToMP4,
    # "QSLoadVideo":QSLoadVideo,
    # "QSVideoZoom": QSVideoZoom,
}
NODE_DISPLAY_NAME_MAPPINGS = {
    # # "QSVideoCombine": "QS Video Combine",
    # "QSVideoCut": "QS Video Cut",
    # "QSVideoConcat": "QS Video Concat",
    # "QSConvertToMP4": "QS Convert To MP4",
    # "QSLoadVideo": "QS Load Video",
    # "QSVideoZoom": "QS Video Zoom",
}
#WEB_DIRECTORY='./js'