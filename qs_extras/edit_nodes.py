import os
import torch
import torchvision
import comfy
from einops import repeat
import skimage as ski
from skimage.measure import find_contours
import numpy
from PIL import Image
import folder_paths
from comfy.cli_args import args
from PIL.PngImagePlugin import PngInfo
import json
from torch.nn import functional as F
import cv2
from collections import deque
import scipy.ndimage as ndimage
import torchvision.transforms.v2 as T
from comfy.model_patcher import ModelPatcher
import math
from torchvision.ops import masks_to_boxes
import matplotlib.pyplot as plt
import io
import numpy as np
import ffmpeg
import shutil
import random
import imageio
from scipy.ndimage import label, center_of_mass


class AnyType(str):
  """A special class that is always equal in not equal comparisons. Credit to pythongosssss"""

  def __ne__(self, __value: object) -> bool:
    return False
any = AnyType("*")


class QSMotionMasks:
    def __init__(self):
        self.cotracker = None
        pass
    
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
            "original_frames": ("IMAGE",),
            "mask_frames": ("MASK",),
            },
            "optional": {
                "device":("STRING", {"default": "cuda"}),
                "num_frames":("INT", {"default": None}),
                "point_mask":("MASK",{"default":None}),
                "track_method":(["cotracker","point_mask","none"], {"default":"none"}),
              }
        }

    RETURN_TYPES = ("MASK", "MASK",)
    RETURN_NAMES = ("motion_masks", "last_motion_mask",)
    FUNCTION = "motion_masks"
    CATEGORY = "QS/tracking"

    def motion_masks(self, original_frames, mask_frames, device, num_frames,point_mask=None, track_method="none"):
        num_frames = num_frames or original_frames.shape[0]
        use_cotracker  = track_method == "cotracker"
        if mask_frames.shape[0] < original_frames.shape[0]:
            motion_masks = repeat(mask_frames, 'b h w -> (repeat b) h w', repeat=original_frames.shape[0]) 
        else:
            motion_masks = mask_frames
        if use_cotracker:
            motion_masks = self.get_motion_masks(original_frames,  mask_frames,device, num_frames)
        elif track_method == "point_mask" and point_mask is not None:
            motion_masks = self.track_point_and_apply_motion(original_frames, mask_frames, point_mask, device, num_frames)
        return (motion_masks, motion_masks[-1])
    
    def get_motion_tracks(self, original_frames,  mask_frames,device,num_frames=None):
        b,h,w,c = original_frames.shape
        num_frames = num_frames or b  #default to batch size
        mask_squeezed = mask_frames.squeeze(0).numpy()
        # Find contours around the perimeter
        contours = find_contours(mask_squeezed, level=0.5)
        # There might be multiple contours; choose the largest one
        largest_contour = max(contours, key=len)
        # Sample approximately 50 points from the largest contour
        num_points = min( len(largest_contour) , 50 )
        step = max(1, len(largest_contour) // num_points)
        sampled_contour = largest_contour[::step]
        # Convert the sampled contour to a tensor of shape [no of points, 2]
        grid = torch.tensor(sampled_contour, dtype=torch.float32)
        # Convert coordinates from (row, col) to (x, y)
        grid = grid[:, [1, 0]]    

        zeros_column = torch.zeros((grid.shape[0], 1), dtype=torch.float32)
        grid = torch.cat((zeros_column, grid), dim=1).unsqueeze(0)

        print(grid.shape)  # Should be (no of points, 2)
        print(grid)
        #changed model to cotracker3g
        if not self.cotracker :
            cotracker = torch.hub.load("facebookresearch/co-tracker", "cotracker3_offline").to(device)
      #      cotracker = self.load_cotracker_model(device=device)
            self.cotracker = cotracker
        with torch.no_grad():
            original_frames=original_frames.clone().unsqueeze(1).permute(0,4,1,2,3).to(device)
            vid_cotracker = ((original_frames+1)/2.).permute(2,0,1,3,4)*255.  # original_image should be b, c, n, w, h 
            grid = grid.to(device)
            tracks, _ = self.cotracker(vid_cotracker, queries=grid) # B T N 2,  B T N 1
            return tracks    

    def get_motion_masks(self, original_frames, mask_frames, device, num_frames=None):
            b,h,w,c = original_frames.shape
            num_frames = num_frames or b  #default to batch size
            with torch.no_grad():
                tracks = self.get_motion_tracks(original_frames,  mask_frames,device,num_frames=None)
                # create a motion_masks tensor from tracks
                # Initialize an empty tensor for masks for all frames
                motion_masks = torch.zeros((num_frames, h, w), dtype=torch.float32)
                # Loop through each frame
                for i in range(num_frames):
                    # Get the x and y coordinates for this frame
                    x_coords = tracks[0, i, :, 0].to('cpu').numpy()
                    y_coords = tracks[0, i, :, 1].to('cpu').numpy()
                    # Create an empty binary mask for this frame
                    m_mask = ski.draw.polygon2mask((h,w),numpy.column_stack((y_coords,x_coords)))
                    # Convert mask to a tensor and store it
                    motion_masks[i] = torch.tensor(m_mask, dtype=torch.float32)
                # The 'masks' tensor now contains recreated masks for each of the 18 frames
                print(motion_masks.shape)  # Should be (18, 768, 1024)
                return motion_masks

    def track_point_and_apply_motion(self, original_frames, mask_frames, point_mask, device, num_frames):
        """
        Tracks the motion of the center of point_mask, and applies the resulting motion
        consistently across all points in mask_frames.

        Args:
            original_frames (Tensor): The original video frames of shape (b, h, w, c).
            mask_frames (Tensor): Binary mask frames of shape (1, h, w).
            point_mask (Tensor): Binary mask representing the point of interest of shape (1, h, w).
            device (str): Device to perform computations ('cuda' or 'cpu').
            num_frames (int): Number of frames to process.

        Returns:
            Tensor: Motion-adjusted mask frames of shape (num_frames, h, w).
        """
        b, h, w, c = original_frames.shape
        mask_squeezed = mask_frames.squeeze(0).numpy()
        point_mask_squeezed = point_mask.squeeze(0).numpy()

        # Find the center point of the point_mask
        center_coords = numpy.argwhere(point_mask_squeezed)
        center = center_coords.mean(axis=0)[::-1]  # (y, x) to (x, y)
        center_point = torch.tensor([0.0]+center.tolist(), dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(device)  # Shape: [1, 1, 2]

        # Initialize CoTracker model
        if not self.cotracker:
            cotracker = torch.hub.load("facebookresearch/co-tracker", "cotracker3_offline").to(device)
            self.cotracker = cotracker

        with torch.no_grad():
            original_frames = original_frames.clone().unsqueeze(1).permute(0, 4, 1, 2, 3).to(device)
            vid_cotracker = ((original_frames + 1) / 2.0).permute(2, 0, 1, 3, 4) * 255.0
            tracks, _ = self.cotracker(vid_cotracker, queries=center_point)  # Shape: [1, num_frames, 1, 2]

        # Compute motion offset for each frame
        motion_offsets = tracks[0, :, 0, :] - tracks[0, 0, 0, :]  # Shape: [num_frames, 2]

        # Initialize motion-adjusted mask frames
        # Get the coordinates of all points in the original mask
        points = numpy.argwhere(mask_squeezed)  # Shape: [num_points, 2]
        motion_masks = torch.zeros((num_frames, h, w), dtype=torch.float32)
                
        for i in range(num_frames):
            offset_x, offset_y = motion_offsets[i].cpu().numpy()
         
            # Apply the motion offset to all points
            new_coords = points + numpy.array([offset_y, offset_x])
            # Clip coordinates to stay within the valid bounds of the frame
            new_coords[:, 0] = numpy.clip(new_coords[:, 0], 0, h - 1)  # y-coordinates
            new_coords[:, 1] = numpy.clip(new_coords[:, 1], 0, w - 1)  # x-coordinates

            # Create a blank mask for the current frame
            shifted_mask = numpy.zeros((h, w), dtype=numpy.float32)
            # Update the mask with the shifted points
            shifted_mask[new_coords[:, 0].astype(int), new_coords[:, 1].astype(int)] = 1
            # Add the shifted mask to the tensor
            motion_masks[i] = torch.tensor(shifted_mask, dtype=torch.float32)
        return motion_masks

class QSInpaintLatentConditioning:
    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
            "original_samples":("LATENT",),
            "motion_masks": ("MASK",),
            "vae": ("VAE",),
            },
            "optional": {
              }
        }

    RETURN_TYPES = ("LATENT", "LATENT",)
    RETURN_NAMES = ("original_samples", "mask_latents",)
    FUNCTION = "inpaint_conditioning"
    CATEGORY = "QS/inpaint"

    def inpaint_conditioning(self, original_samples, motion_masks, vae):
        
        original_latents = original_samples['samples'] #B T C H W  (T= (t-1)/4 + 1,  H=h/8, W=w/8)
        #get the dims of original_latents
        B,T,C, H,W = original_latents.shape
        t,h,w = motion_masks.shape
        h_scale = h//H 
        w_scape = w//W 
        
        if t > 1:
            # taken maks
            indices = torch.linspace(0, t - 1, steps=T).round().long()
            motion_masks = motion_masks[indices]  # Shape becomes (t_new, h, w)
            assert len(indices) == T
        else :
            # repeat 1st frame T times
            motion_masks = motion_masks.repeat(T,1,1) 
        motion_masks = F.interpolate(motion_masks.unsqueeze(0), size=( H,W), mode='nearest').squeeze(0)
        # add dim 1 and repeat it C times
        motion_masks = motion_masks.unsqueeze(0).unsqueeze(2).repeat(1,1,C,1,1)
        # check motion mask t dim is 
        original_samples['motion_masks'] = motion_masks
        mask_latents = {'samples': motion_masks} 
        return (original_samples, mask_latents)

class QSImageComposite:
    def __init__(self):
        pass
    
    
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
            "image_a": ("IMAGE",),
            "image_b": ("IMAGE",),
            "image_b_x": ("INT",{"default":0}),
            "image_b_y": ("INT",{"default":0}),
            },
        }

    RETURN_TYPES = ("IMAGE",)
    RETURN_NAMES = ("Image",)
    FUNCTION = "image_composite"
    CATEGORY = "QS/utils"

    def image_composite(self, image_a, image_b, image_b_x,image_b_y):
        t,  h_image_b, w_image_b,c = image_b.shape
        _,  h_image_a, w_image_a,c = image_a.shape
        result_image = image_a.clone()
        # Ensure image_b fits within image_a at the given position
        assert image_b_y + h_image_b <= h_image_a and image_b_x + w_image_b <= w_image_a, "image_b exceeds image_a's dimensions at the given position"
        
        # Paste image_b on top of image_a
        result_image[:, image_b_y:image_b_y + h_image_b, image_b_x:image_b_x + w_image_b,:] = image_b
        return (result_image,)

class QSImageBatchCombine:
    def __init__(self):
        pass
    
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
            "image1": ("IMAGE",),
            "image2": ("IMAGE",),
            "group_size_image1": ("INT",{"default":1}),
            "group_size_image2": ("INT",{"default":1}),
            },
        }

    RETURN_TYPES = ("IMAGE",)
    RETURN_NAMES = ("Combined Image",)
    FUNCTION = "interleave_tensors"
    CATEGORY = "QS/utils"

    def interleave_tensors(self,image1, image2, group_size_image1, group_size_image2):
        # Validate input tensors
        if image1.ndim != 4 or image2.ndim != 4:
            raise ValueError("Both input tensors must have 4 dimensions (batch, channels, height, width).")

        if image1.shape[1:] != image2.shape[1:]:
            raise ValueError("Channel, height, and width dimensions of both tensors must match.")

        # Get the shapes of the tensors
        b1, c, h, w = image1.shape
        b2, _, _, _ = image2.shape

        # Interleave in grouped indices based on group_sizes
        combined = []
        i, j = 0, 0

        while i <= b1 - group_size_image1 or j <= b2 - group_size_image2:
            if i <= b1 - group_size_image1:
                combined.extend(image1[i:i+group_size_image1])  # Add group1 frames from image1
                i += group_size_image1
            if j <= b2 - group_size_image2:
                combined.extend(image2[j:j+group_size_image2])  # Add group2 frames from image2
                j += group_size_image2

        # Stack combined frames into a tensor
        try:
            return (torch.stack(combined, dim=0),)
        except RuntimeError as e:
            raise RuntimeError(f"Error while stacking tensors: {e}")
       
class QSMaskConverter:
    def __init__(self):
        pass
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
            },
            "optional": {
              }
        }

    RETURN_TYPES = ("FUNCTION",)
    RETURN_NAMES = ("MaskConverter", )
    FUNCTION = "mask_converter"
    CATEGORY = "QS/inpaint"

    def mask_converter(self):
        
        def inpaint_conditioning(original_latents, motion_masks):

            """
            Function to convert motion masks to latent masks for QSInpaintLatentConditioning.
            
            Parameters:
            original_samples (dict): Dictionary containing the original samples.
            motion_masks (torch.Tensor): 3D tensor of shape (t, h, w) representing the motion masks.
            
            Returns:
            tuple: A tuple containing the original samples with an additional key 'latent_masks' containing the converted motion masks, and the converted motion masks.
            """
            #get the dims of original_latents
            B,C,T, H,W = original_latents.shape
            t,h,w = motion_masks.shape
            h_scale = h/H 
            w_scape = w/W 
            # match t with T 
            motion_masks = F.interpolate(motion_masks.unsqueeze(0), size=(T, h,w), mode='nearest').squeeze(0)
            # now match the h and w with H and W
            motion_masks = F.interpolate(motion_masks.unsqueeze(0), size=(T, h,w), mode='nearest').squeeze(0)
            # add dim 1 and repeat it C times
            motion_masks = motion_masks.unsqueeze(1).repeat(1,C,1,1,1)
            # check motion mask t dim is 
            return (motion_masks,)
        return (inpaint_conditioning,)
    
class QSSquarePaddingData:
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
            "width": ("INT",),
            "height": ("INT",),
        }}

    RETURN_TYPES = ("INT","INT","INT","INT", )
    RETURN_NAMES = ("left", "top", "right", "bottom",)
    FUNCTION = "calculate_square_padding"
    CATEGORY = "QS/utils"

    def calculate_square_padding(self, width, height):
        if width > height:
            # Add padding to height
            diff = width - height
            left = 0
            top = diff // 2
            right = 0
            bottom = diff - top

        elif height > width:
            # Add padding to width
            diff = height - width
            left = diff // 2
            top = 0
            right = diff - left
            bottom = 0

        else:
            left = 0
            top = 0
            right = 0
            bottom = 0

        return (left, top, right, bottom)

class QSGaussianBlurMask:
    def __init__(self):
        pass
    
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
            "mask": ("MASK", ),
            "kernel_size": ("INT", {"default": 10, "min": 0, "max": 100, "step": 1}),
            "sigma": ("FLOAT", {"default": 10.0, "min": 0.1, "max": 100.0, "step": 0.1}),
            }
        }

    RETURN_TYPES = ("MASK",)
    RETURN_NAMES = ("Mask",)
    FUNCTION = "doit"
    CATEGORY = "QS/mask"

    def make_3d_mask(self, mask):
        if len(mask.shape) == 4:
            return mask.squeeze(0)

        elif len(mask.shape) == 2:
            return mask.unsqueeze(0)

        return mask
    
    def tensor_gaussian_blur_mask(self, mask, kernel_size, sigma):
        """Return NHWC torch.Tenser from ndim == 2 or 4 `numpy.ndarray` or `torch.Tensor`"""
        if isinstance(mask, numpy.ndarray):
            mask = torch.from_numpy(mask)

        if mask.ndim == 2:
            mask = mask[None, ..., None]
        elif mask.ndim == 3:
            mask = mask[..., None]

        if kernel_size <= 0:
            return mask

        kernel_size = kernel_size*2+1

        shortest = min(mask.shape[1], mask.shape[2])
        if shortest <= kernel_size:
            kernel_size = int(shortest/2)
            if kernel_size % 2 == 0:
                kernel_size += 1
            if kernel_size < 3:
                return mask  # skip feathering

        prev_device = mask.device
        device = comfy.model_management.get_torch_device()
        mask.to(device)

        # apply gaussian blur
        mask = mask[:, None, ..., 0]
        blurred_mask = torchvision.transforms.GaussianBlur(kernel_size=kernel_size, sigma=sigma)(mask)
        blurred_mask = blurred_mask[:, 0, ..., None]

        blurred_mask.to(prev_device)

        return blurred_mask

    def doit(self, mask, kernel_size, sigma):
        # Some custom nodes use abnormal 4-dimensional masks in the format of b, c, h, w. In the impact pack, internal 4-dimensional masks are required in the format of b, h, w, c. Therefore, normalization is performed using the normal mask format, which is 3-dimensional, before proceeding with the operation.
        mask = self.make_3d_mask(mask)
        mask = torch.unsqueeze(mask, dim=-1)
        mask = self.tensor_gaussian_blur_mask(mask, kernel_size, sigma)
        mask = torch.squeeze(mask, dim=-1)
        return (mask, )


class UniformMaskNode:
    def __init__(self):
        pass
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "mask": ("MASK",),  # Expecting a binary tensor mask
                "center_value": ("FLOAT", {"default": 0.99, "min": 0.0, "max": 1.00, "step": 0.01}),
                "boundary_value": ("FLOAT", {"default": 0.94, "min": 0.0, "max": 1.00, "step": 0.01}),
                "up_down": ("FLOAT", {"default": 0.0, "min": -1.00, "max": 1.00, "step": 0.01}),
                "left_right": ("FLOAT", {"default": 0.0, "min": -1.00, "max": 1.00, "step": 0.01}),
            },

            "optional": {
                "point_mask":("MASK",),
            },
        }

    RETURN_TYPES = ("MASK","STRING",)
    RETURN_NAMES = ("Mask","Output",)
    FUNCTION = "apply_gradient"
    CATEGORY = "QS/mask"

    def apply_gradient(self, mask, center_value, boundary_value, up_down, left_right, point_mask=None):
        out=""
        mask_tensor=mask
    
        # Ensure shape is (1, H, W) and remove batch dimension
        if mask_tensor.ndim == 3 and mask_tensor.shape[0] == 1:
            mask_tensor = mask_tensor.squeeze(0)  # Convert (1, H, W) -> (H, W)

        indices = torch.nonzero(mask_tensor, as_tuple=False)
        
        if len(indices) == 0:
            return (mask_tensor.unsqueeze(0),)  # Restore batch dimension (1, H, W)

        # Get bounding box of the rectangle
        min_row, min_col = indices.min(dim=0).values
        max_row, max_col = indices.max(dim=0).values
        center_row, center_col = ((min_row + max_row)+(max_row - min_row)*up_down) // 2, ((min_col + max_col)+(max_col-min_col)*left_right) // 2
        out="Your center is not a point mask, " + str(center_row) + " " + str(center_col)

        if point_mask is not None:
            # Ensure shape is (1, H, W) and remove batch dimension
            if point_mask.ndim == 3 and point_mask.shape[0] == 1:
                point_mask = point_mask.squeeze(0)  # Convert (1, H, W) -> (H, W)
            indice = torch.nonzero(point_mask, as_tuple=False)
            
            if len(indice) != 0:
                # Get bounding box of the rectangle
                min_row1, min_col1 = indice.min(dim=0).values
                max_row1, max_col1 = indice.max(dim=0).values
                center_row, center_col = (min_row1 + max_row1) // 2, (min_col1 + max_col1) // 2
                out="Your center is a point mask, " + str(center_row) + " " + str(center_col)

        # Create row and column distance grids
        rows = torch.arange(mask_tensor.shape[0], device=mask_tensor.device).view(-1, 1)
        cols = torch.arange(mask_tensor.shape[1], device=mask_tensor.device).view(1, -1)

        # Compute normalized distances
        dist_row = torch.abs(rows - center_row) / ((max_row - min_row) / 2 + 1e-6)  # Avoid divide by zero
        dist_col = torch.abs(cols - center_col) / ((max_col - min_col) / 2 + 1e-6)
        
        # Get max distance (radial effect)
        normalized_dist = torch.maximum(dist_row, dist_col)

        # Interpolate values only inside the rectangle
        output = mask_tensor.clone()
        output[mask_tensor > 0] = center_value * (1 - normalized_dist[mask_tensor > 0]) + boundary_value * normalized_dist[mask_tensor > 0]

        return (output.unsqueeze(0),out,)
    
class BlendThroughMask:
    def __init__(self):
        pass
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "mask": ("MASK",),  # Binary mask input
                "shift_pixels": ("INT", {"default": 5, "min": 1, "max": 100, "step": 1}),
                "boundary_value": ("FLOAT", {"default": 0.50, "min": 0.00, "max": 1.00, "step": 0.01}),
                "mid_value": ("FLOAT", {"default": 1.00, "min": 0.00, "max": 1.00, "step": 0.01}),
            }
        }

    RETURN_TYPES = ("MASK",)
    RETURN_NAMES = ("Mask",)
    FUNCTION = "apply_gradient"
    CATEGORY = "QS/mask"

    def apply_gradient(self, mask, shift_pixels, boundary_value, mid_value):
        mask_tensor = mask.clone().squeeze().to('cpu')  # Ensure it's on GPU
        h, w = mask_tensor.shape

        # Define a 3x3 kernel to find boundary pixels
        kernel = torch.tensor([[1, 1, 1],
                               [1, 0, 1],
                               [1, 1, 1]], dtype=torch.float32, device='cpu').view(1, 1, 3, 3)

        # Apply convolution to detect boundaries
        mask_exp = mask_tensor.unsqueeze(0).unsqueeze(0).float()  # Shape: (1, 1, H, W)
        neighbors = F.conv2d(mask_exp, kernel, padding=1).squeeze()
        boundary = (mask_tensor > 0) & (neighbors < 8)  # Detect edge pixels

        # BFS to propagate values
        queue = deque(torch.nonzero(boundary, as_tuple=False).tolist())
        flag = torch.zeros_like(mask_tensor, dtype=torch.bool)
        flag[boundary] = True

        for c in range(shift_pixels, 0, -1):
            if not queue:
                break

            value = (shift_pixels * mid_value - (mid_value - boundary_value) * c) / shift_pixels
            next_queue = deque()

            while queue:
                i, j = queue.popleft()
                mask_tensor[i, j] = value

                # Check 8 neighbors
                for di, dj in [(-1, 0), (-1, -1), (-1, 1), (0, -1), (0, 1), (1, 0), (1, -1), (1, 1)]:
                    ni, nj = i + di, j + dj
                    if 0 <= ni < h and 0 <= nj < w and mask_tensor[ni, nj] > 0 and not flag[ni, nj]:
                        flag[ni, nj] = True
                        next_queue.append((ni, nj))

            queue = next_queue  # Move to the next expansion layer

        return (mask_tensor.unsqueeze(0),)



class SplitMaskIntoPatches():
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {"mask": ("MASK",)}}

    RETURN_TYPES = ("MASK",)
    FUNCTION = "split_mask"
    CATEGORY = "QS/mask"

    def split_mask(self, mask: torch.Tensor):
        mask = mask.squeeze(0)  # Shape: (h, w)
        
        # Label connected components using SciPy
        mask_np = mask.numpy()  # Convert to NumPy (CPU)
        labeled_mask_np, num_patches = ndimage.label(mask_np)  # Compute connected components
        labeled_mask = torch.tensor(labeled_mask_np, device="cpu")
        
        if num_patches == 0:
            return mask.unsqueeze(0)  # Return same mask if no patches found
        
        h, w = mask.shape
        batch_masks = torch.zeros((num_patches, h, w), device=mask.device)
        
        for i in range(1, num_patches + 1):
            batch_masks[i-1] = (labeled_mask == i)
        
        return (batch_masks,)
    


class QSMaskBoundingBox:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "mask": ("MASK",),
                "padding": ("INT", {"default": 0, "min": 0, "max": 4096, "step": 1}),
                "blur": ("INT", {"default": 0, "min": 0, "max": 256, "step": 1}),
            },
            "optional": {
                "image_optional": ("IMAGE",),
            }
        }

    RETURN_TYPES = ("MASK", "IMAGE")
    RETURN_NAMES = ("batch_masks", "batch_images")
    FUNCTION = "execute"
    CATEGORY = "QS/mask"

    def execute(self, mask, padding, blur, image_optional=None):
        if mask.dim() == 2:
            mask = mask.unsqueeze(0)  # Convert (H, W) → (1, H, W)

        batch_size, h, w = mask.shape  # Get batch size

        if image_optional is None:
            image_optional = mask.unsqueeze(3).repeat(1, 1, 1, 3)  # Convert to RGB

        processed_masks = []
        processed_images = []

        # Define blur transformation
        blur_transform = T.GaussianBlur(kernel_size=(blur, blur)) if blur > 0 else None

        for i in range(batch_size):
            current_mask = mask[i]  # Get the mask for current batch element

            # Blur the mask if needed
            if blur_transform:
                current_mask = blur_transform(current_mask.unsqueeze(0)).squeeze(0)

            # Find bounding box for current mask
            coords = torch.where(current_mask)
            if len(coords[0]) == 0:  # If mask is empty, skip
                continue

            y1, y2 = max(0, coords[0].min().item() - padding), min(h, coords[0].max().item() + 1 + padding)
            x1, x2 = max(0, coords[1].min().item() - padding), min(w, coords[1].max().item() + 1 + padding)

            # Crop mask and image
            cropped_mask = current_mask[y1:y2, x1:x2]
            cropped_image = image_optional[0, y1:y2, x1:x2, :]

            # Make square by adding padding
            crop_h, crop_w = cropped_mask.shape
            max_dim = max(crop_h, crop_w)

            pad_top = (max_dim - crop_h) // 2
            pad_bottom = max_dim - crop_h - pad_top
            pad_left = (max_dim - crop_w) // 2
            pad_right = max_dim - crop_w - pad_left

            square_mask = torch.nn.functional.pad(cropped_mask, (pad_left, pad_right, pad_top, pad_bottom), value=0)
            square_image = torch.nn.functional.pad(cropped_image.permute(2, 0, 1), (pad_left, pad_right, pad_top, pad_bottom), value=0).permute(1, 2, 0)

            # Resize to 1024x1024
            resized_mask = torch.nn.functional.interpolate(
                square_mask.unsqueeze(0).unsqueeze(0), size=(1024, 1024), mode='area'
            ).squeeze(0).squeeze(0)

            resized_image = torch.nn.functional.interpolate(
                square_image.permute(2, 0, 1).unsqueeze(0), size=(1024, 1024), mode='area'
            ).squeeze(0).permute(1, 2, 0)

            processed_masks.append(resized_mask)
            processed_images.append(resized_image)

        # Stack into batched tensors
        batch_masks = torch.stack(processed_masks, dim=0)
        batch_images = torch.stack(processed_images, dim=0)

        return (batch_masks, batch_images)

        
class AntiAliasingNode:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "image": ("IMAGE",),
                "method": (["SSAA", "MSAA", "FXAA", "TAA", "SMAA", "DLSS"], {"default": "FXAA"}),
            }
        }
    
    RETURN_TYPES = ("IMAGE",)
    FUNCTION = "apply_anti_aliasing"
    CATEGORY = "QS/utils"
    
    def apply_anti_aliasing(self, image, method):
        # Convert tensor to numpy array
        if isinstance(image, torch.Tensor):
            image = (image.squeeze(0).permute(1, 2, 0).cpu().numpy() * 255).astype(numpy.uint8)

        # Ensure image is RGB (remove alpha channel if present)
        if image.shape[-1] == 4:
            image = image[:, :, :3]
        
        if method == "SSAA":
            image = self.ssaa(image)
        elif method == "MSAA":
            image = self.msaa(image)
        elif method == "FXAA":
            image = self.fxaa(image)
        elif method == "TAA":
            image = self.taa(image)
        elif method == "SMAA":
            image = self.smaa(image)
        elif method == "DLSS":
            image = self.dlss(image)
        
        return (torch.from_numpy(image.astype(numpy.float32) / 255.0).permute(2, 0, 1).unsqueeze(0),)
    
    def ssaa(self, img):
        scale = 2  # Super Sample Factor
        h, w = img.shape[:2]
        img_high_res = cv2.resize(img, (w * scale, h * scale), interpolation=cv2.INTER_CUBIC)
        return cv2.resize(img_high_res, (w, h), interpolation=cv2.INTER_AREA)
    
    def msaa(self, img):
        return cv2.GaussianBlur(img, (5, 5), 0)
    
    def fxaa(self, img):
        if img.dtype != numpy.uint8:
            img = (img * 255).clip(0, 255).astype(numpy.uint8)  # Normalize and convert to uint8
        if len(img.shape) == 2:  # If grayscale, convert to 3-channel
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)
        return cv2.bilateralFilter(img, 9, 75, 75)

    
    def taa(self, img):
        img = img.astype(numpy.uint8)
        return cv2.GaussianBlur(img, (3, 3), 0)
    
    def smaa(self, img):
        img = img.astype(numpy.uint8)
        return cv2.medianBlur(img, 3)
    
    def dlss(self, img):
        img = img.astype(numpy.uint8)
        # Placeholder for DLSS, requires NVIDIA's deep learning-based scaling
        return cv2.GaussianBlur(img, (3, 3), 0)  # Temporary approximation



class devicecheck:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "any_input": (any,{}),
                "device": (["cuda:0", "cpu"], {"default": "cuda:0"})
            }
        }
    
    RETURN_TYPES = (any,"STRING",)
    FUNCTION = "device_check"
    CATEGORY = "QS"
    
    def device_check(self, any_input, device):
        st=any_input.device
        if device!=st:
            any_input=any_input.to(device)
        ts="You initial device is "+str(st)+", now your device is "+str(any_input.device) 
        print(ts)
        return (any_input,ts,)


class QSPasteByMaskBatchCombine:
    """
    Pastes a batch of `image_to_paste` onto a single `image_base` using `mask` to determine placement.
    The pasting is sequential: each `image_to_paste[i]` is pasted on top of the modified base image.
    """
    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "image_base": ("IMAGE",),  # (1, H, W, C) -> RGB
                "image_to_paste": ("IMAGE",),  # (B, H1, W1, C1) -> RGBA
                "mask": ("MASK",),  # (B, H, W)
                "resize_behavior": (["resize", "source_size"],)
            },
        }
    INPUT_IS_LIST = True
    RETURN_TYPES = ("IMAGE",)
    FUNCTION = "paste"
    CATEGORY = "QS/image"

    def paste(self, image_base, image_to_paste, mask, resize_behavior):
        image_base = image_base[0].clone()  # (1, H, W, C) -> RGB
        mask = mask[0].float()
        
        B, H, W = mask.shape
        
        # Process each image_to_paste[i] and apply it sequentially
        for i in range(B):
            current_mask = mask[i].unsqueeze(0)  # (1, H, W)
            
            # Avoid issues with empty masks
            is_empty = current_mask.max() == 0
            if is_empty:
                continue
            
            boxes = masks_to_boxes(current_mask)
            min_x, min_y, max_x, max_y = boxes[0]
            width, height = int(max_x - min_x + 1), int(max_y - min_y + 1)
            
            # Resize if necessary
            if resize_behavior == "resize":
                resized_image = torch.nn.functional.interpolate(
                    image_to_paste[i].permute(0, 3, 1, 2),
                    size=(height, width),
                    mode='area'
                ).permute(0, 2, 3, 1)
            else:  # "source_size"
                resized_image = image_to_paste[i]
            
            # Determine pasting region
            ymin, xmin = max(0, int(min_y)), max(0, int(min_x))
            ymax, xmax = min(H, ymin + height), min(W, xmin + width)
            
            paste_region = resized_image[:, :ymax-ymin, :xmax-xmin, :]
            alpha_mask = paste_region[..., 3:4]  # Extract alpha channel
            
            # Apply alpha blending
            image_base[0, ymin:ymax, xmin:xmax, :] = (
                paste_region[..., :3] * alpha_mask +
                image_base[0, ymin:ymax, xmin:xmax, :] * (1 - alpha_mask)
            )
        
        return (image_base,)



class AlphaLevelsNode:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "mask": ("MASK",),  # Expects a mask tensor
                "black_point": ("INT", {"default": 0, "min": 0, "max": 255, "step": 1}),
                "white_point": ("INT", {"default": 255, "min": 0, "max": 255, "step": 1}),
            }
        }

    RETURN_TYPES = ("MASK",)
    FUNCTION = "apply_levels"
    CATEGORY = "QS/mask"
    
    def apply_levels(self, mask, black_point, white_point):
        # Convert tensor to numpy
        mask_np = mask.cpu().numpy()[0, :, :]
        
        # Normalize to 0-1 range based on 8-bit (0-255) input
        mask_np = (mask_np * 255 - black_point) / (white_point - black_point)
        mask_np = numpy.clip(mask_np, 0, 1)
        
        # Convert back to tensor
        mask_tensor = torch.tensor(mask_np).unsqueeze(0).unsqueeze(0)
        
        return (mask_tensor,)

class GammaCorrection:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "image": ("IMAGE",),  # Expects a mask tensor
                "gamma": ("FLOAT", {"default": 1.00, "min": 0.01, "max": 10.00, "step": 0.01}),
            }
        }

    RETURN_TYPES = ("IMAGE",)
    FUNCTION = "apply_levels"
    CATEGORY = "QS/image"
    
    def apply_levels(self, image, gamma):
        """Applies gamma correction to an image, excluding the alpha channel."""
        # Ensure image is float32 and in range [0,1]
        image = image.float().clamp(0, 1)
        
        # Separate color channels and alpha channel (if present)
        if image.shape[-1] == 4:  # Assuming (B, H, W, C) format
            rgb = image[..., :3]
            alpha = image[..., 3:]
            
            # Apply gamma correction only on RGB channels
            corrected_rgb = rgb ** (1.0 / gamma)
            
            # Concatenate back with alpha channel
            corrected_image = torch.cat([corrected_rgb, alpha], dim=-1)
        else:
            corrected_image = image ** (1.0 / gamma)
        
        return (corrected_image,)

class MaskVisualizationNode:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "mask": ("MASK",),
                "plot_type": (["grayscale", "heatmap", "histogram", "colorful_heatmap"],)
            }
        }

    RETURN_TYPES = ("IMAGE",)
    FUNCTION = "visualize_mask"
    CATEGORY = "QS/mask"

    def visualize_mask(self, mask, plot_type):
        # Convert torch tensor to numpy array
        if isinstance(mask, torch.Tensor):
            mask = mask.squeeze().cpu().numpy()

        # Normalize mask values between 0 and 1
        mask = (mask - mask.min()) / (mask.max() - mask.min() + 1e-8)

        height, width = mask.shape
        dpi = 100  # Set DPI for high quality
        

        figsize = (width / dpi, height / dpi)

        # Create figure
        fig, ax = plt.subplots(figsize=figsize, dpi=dpi)
        
        if plot_type == "grayscale":
            ax.imshow(mask, cmap='gray', vmin=0, vmax=1)
            ax.set_title("Grayscale Mask")
            ax.axis("off")
        
        elif plot_type == "heatmap":
            ax.imshow(mask, cmap='viridis', vmin=0, vmax=1)
            ax.set_title("Heatmap Visualization")
            ax.axis("off")
        
        elif plot_type == "colorful_heatmap":
            ax.imshow(mask, cmap='turbo', vmin=0, vmax=1)
            ax.set_title("Enhanced Colorful Heatmap")
            ax.axis("off")
        
        elif plot_type == "histogram":
            ax.hist(mask.ravel(), bins=50, range=(0,1), color='blue', alpha=0.7)
            ax.set_title("Alpha Value Histogram")
            ax.set_xlabel("Alpha Value")
            ax.set_ylabel("Frequency")
        
        # Save the plot to an image
        buf = io.BytesIO()
        plt.savefig(buf, format='PNG', bbox_inches='tight')
        plt.close(fig)
        buf.seek(0)
        
        # Convert to PIL Image and then to tensor
        image = Image.open(buf).convert("RGB")
        image = torch.from_numpy(numpy.array(image)).float() / 255.0
        image = image.permute(2, 0, 1)  # Convert to (C, H, W)
        
        return (image,)
    

class QSMeanBlurNode:
    """Applies a Mean Blur (Box Blur) to a mask tensor of shape (B, H, W) in ComfyUI."""
    
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "mask": ("MASK",),  # Expecting a 3D tensor (B, H, W)
                "kernel_size": ("INT", {"default": 5, "min": 1, "max": 31, "step": 1}),
            }
        }

    RETURN_TYPES = ("MASK",)
    FUNCTION = "apply_mean_blur"
    CATEGORY = "QS/mask"

    def apply_mean_blur(self, mask, kernel_size):
        if kernel_size % 2 == 0:
            kernel_size += 1  # Ensure kernel size is odd

        # Add a channel dimension (B, C=1, H, W) for pooling
        mask = mask.unsqueeze(1)

        # Apply mean blur
        blurred_mask = F.avg_pool2d(mask, kernel_size, stride=1, padding=kernel_size // 2)

        # Remove the channel dimension (back to B, H, W)
        return (blurred_mask.squeeze(1),)



class QS2DTRACKING():
    def __init__(self):
        pass
    @classmethod
    def INPUT_TYPES(cls):
        return {"required": {"mask": ("IMAGE",), "original_frames": ("IMAGE",), "save_txt": ("BOOLEAN",), "save_directory": ("STRING",)}}

    RETURN_TYPES = ("TENSOR", "TENSOR")
    FUNCTION = "compute_centroids"
    CATEGORY = "QS/tracking"

    def compute_centroids(self, mask: torch.Tensor, original_frames: torch.Tensor, save_txt: bool, save_directory: str):
        device = mask.device
        mask_np = mask.squeeze(0).numpy()  # Convert to NumPy (h, w)
        labeled_mask, num_patches = label(mask_np)  # Get connected components
        
        if num_patches == 0:
            return torch.empty((0, 2)), torch.empty((0, mask_np.shape[0], mask_np.shape[1]))  # No patches found
        
        centroids = np.array(center_of_mass(mask_np, labeled_mask, range(1, num_patches + 1)))
        centroids_tensor = torch.tensor(centroids, dtype=torch.float32, device=device)
        
        # Tracking centroids using CoTracker
        b, h, w, c = original_frames.shape
        num_frames = b
        
        if not hasattr(self, 'cotracker'):
            self.cotracker = torch.hub.load("facebookresearch/co-tracker", "cotracker3_offline").to(device)
        
        with torch.no_grad():
            original_frames = original_frames.clone().unsqueeze(1).permute(0, 4, 1, 2, 3).to(device)
            vid_cotracker = ((original_frames + 1) / 2.0).permute(2, 0, 1, 3, 4) * 255.0
            centroids_reshaped = torch.cat([torch.zeros((centroids_tensor.shape[0], 1), device=device), centroids_tensor], dim=1)
            centroids_reshaped = centroids_reshaped.unsqueeze(1)  # Shape: [num_centroids, 1, 3]
            
            tracks, _ = self.cotracker(vid_cotracker, queries=centroids_reshaped)  # Shape: [num_centroids, num_frames, 1, 2]
        
        motion_offsets = tracks[:, :, 0, :] - tracks[:, 0, 0, :]  # Shape: [num_centroids, num_frames, 2]
        
        # Motion-adjusted masks per frame
        motion_masks = torch.zeros((num_frames, h, w), dtype=torch.float32, device=device)
        points = np.argwhere(mask_np)  # All pixel coordinates of 1s in mask
        
        for i in range(num_frames):
            shifted_mask = np.zeros((h, w), dtype=np.float32)
            
            for j in range(num_patches):
                offset_x, offset_y = motion_offsets[j, i].cpu().numpy()
                new_coords = points + np.array([offset_y, offset_x])
                new_coords[:, 0] = np.clip(new_coords[:, 0], 0, h - 1)  # y-coordinates
                new_coords[:, 1] = np.clip(new_coords[:, 1], 0, w - 1)  # x-coordinates
                shifted_mask[new_coords[:, 0].astype(int), new_coords[:, 1].astype(int)] = 1
            
            motion_masks[i] = torch.tensor(shifted_mask, dtype=torch.float32, device=device)
        
        # Save centroid tracking data to a text file if enabled
        if save_txt:
            os.makedirs(save_directory, exist_ok=True)
            file_path = os.path.join(save_directory, "centroid_tracking.txt")
            with open(file_path, "w") as f:
                for j in range(num_patches):
                    for i in range(num_frames):
                        x, y = tracks[j, i, 0, :].cpu().numpy()
                        f.write(f"{x} {y}\n")
                    f.write("\n")
        
        return centroids_tensor, motion_masks


    
    
        
NODE_CLASS_MAPPINGS = {
    "QSSquarePaddingData" : QSSquarePaddingData,
    "QSInpaintLatentConditioning": QSInpaintLatentConditioning,
    "QSImageComposite": QSImageComposite,
    "QSMotionMasks": QSMotionMasks,
    "QSMaskConverter": QSMaskConverter,
    "QSImageBatchCombine": QSImageBatchCombine,
    "QSGaussianBlurMask": QSGaussianBlurMask,
    "UniformMaskNode":UniformMaskNode,
    "BlendThroughMask": BlendThroughMask,
    "SplitMaskIntoPatches": SplitMaskIntoPatches,
    "QSMaskBoundingBox": QSMaskBoundingBox,
    "AntiAliasingNode": AntiAliasingNode,
    "devicecheck": devicecheck,
    "QSPasteByMaskBatchCombine": QSPasteByMaskBatchCombine,
    "AlphaLevels": AlphaLevelsNode,
    "MaskVisualizationNode": MaskVisualizationNode,
    "GammaCorrection": GammaCorrection,
    "QSMeanBlurNode": QSMeanBlurNode,
    "QS2DTRACKING": QS2DTRACKING,
}
NODE_DISPLAY_NAME_MAPPINGS = {
    "QSInpaintLatentConditioning": "Inpaint Latent Conditioning",
    "QSImageComposite": "Image Composite",
    "QSMotionMasks": "Motion Masks",
    "QSMaskConverter": "Mask Converter",
    "QSImageBatchCombine": "Image Batch Combine",
    "QSSquarePaddingData" : "Qs Square Padding",
    "QSGaussianBlurMask": "QS Gaussian Blur Mask",
    # "QSSaveImage": "QS Save Image",
    "UniformMaskNode": "QS Uniform Mask",
    "QSCFGGuiderNode": "QS CFG Guider Node",
    "QSGuiderMixerNode" : "QS Guider Mixer Node",
    "QSInpaintGuiderNode": "QS Inpaint Guider Node",
    "BlendThroughMask": "QS Blend Through Mask",
    "SplitMaskIntoPatches": "QS Split Mask Into Patches",
    "QSMaskBoundingBox": "QS Mask Bounding Box",
    "AntiAliasingNode": "QS Anti-Aliasing Node",
    "devicecheck": "QS Device Change",
    "QSPasteByMaskBatchCombine": "QS Recursive Paste By Mask",
    "AlphaLevels": "QS Alpha Levels Adjustment",
    "MaskVisualizationNode": "QS Mask Visualization",
    "GammaCorrection": "QS Gamma Correction",
    "QSMeanBlurNode": "QS Mean Blur Node",
    "QS2DTRACKING": "QS 2D Tracking",
 
}

# class QSCFGMixerGuider(QSCFGGuider):
#     def __init__(self, g0, g1, g2, g3,  model_mixing_steps):
#         super().__init__(g0.model_patcher)
#         self.guiders = {k: v for k, v in {"g0": g0, "g1": g1, "g2": g2, "g3": g3}.items() if v is not None}
#         self.model_patchers = {k: guider.model_patcher for k, guider in self.guiders.items()}
#         self.inner_models = {}
#         self.loaded_models_all = {}
#         self.conds_all = {}
#         # Convert model_mixing_steps to dictionary
#         model_mixing_steps_new = deepcopy(model_mixing_steps)
#         try:
#             model_mixing_steps_new = json.loads(model_mixing_steps_new)
#         except:
#             model_mixing_steps_new = {"g0": [], "remaining_steps": "g0"}
        
#         self.remaining_steps_model = model_mixing_steps_new.get("remaining_steps", None)
#         del model_mixing_steps_new["remaining_steps"]
#         self.model_mixing_steps = model_mixing_steps_new

#     # def __call__(self, *args, **kwargs):
#     #     return self.predict_noise(*args, **kwargs)

#     def get_inner_model(self, timestep):
#         """Determine the inner model based on timestep."""
#         indices = torch.isclose(self.sigmas, timestep).nonzero(as_tuple=True)[0]
#         index = indices.item() if indices.numel() > 0 else None        
#         for model_name in self.model_mixing_steps:
#             if index in self.model_mixing_steps[model_name]:
#                 return self.inner_models[model_name]
#         # not found then use remaining_steps model
#         if self.remaining_steps_model:
#             return self.inner_models[self.remaining_steps_model]
#         else :
#             return None
        
#     def predict_noise(self, x, timestep, model_options={}, seed=None):
#         inner_model = self.get_inner_model(timestep)
#         if inner_model is None:
#             return x
#         return comfy.samplers.sampling_function(
#             inner_model, x, timestep, self.conds.get("negative", None),
#             self.conds.get("positive", None), self.cfg, model_options=model_options, seed=seed
#         )
        
#     def pre_sample(self, noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed):
#         # get the inner model for each guider
#         for key, model_patcher in self.model_patchers.items():
#             if key == "g0":
#                 [self.inner_models[key], self.conds_all[key], self.loaded_models_all[key]]  = [self.inner_model,self.conds,self.loaded_models]
#                 continue
#             self.inner_models[key], self.conds_all[key], self.loaded_models_all[key] = comfy.sampler_helpers.prepare_sampling(model_patcher, noise.shape, self.conds)

#     def post_sample(self, noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed, output):
#         #delete except g0 model
#         for key, inner_model in self.inner_models.items():
#             if key != "g0":
#                 try :
#                     del inner_model
#                 except:
#                     pass
#                 try:
#                     del self.conds_all[key]
#                 except:
#                     pass
#                 try:
#                     del self.loaded_models_all[key]
#                 except:
#                     pass
#         return output


# class QSGuiderMixerNode:
#     @classmethod
#     def INPUT_TYPES(s):
#         return {
#             "required": {
#                 "g0": ("GUIDER",),
#                 "positive": ("CONDITIONING",),
#                 "negative": ("CONDITIONING",),
#       #          "cfg": ("FLOAT", {"default": 8.0, "min": 0.0, "max": 100.0, "step": 0.1, "round": 0.01}),
#             },
#             "optional": {
#                 "g1": ("GUIDER",),
#                 "g2": ("GUIDER",),
#                 "g3": ("GUIDER",),
#                 "model_mixing_steps": ("STRING", {"default": '{"g0":[], "remaining_steps":"g0"}', "multiline": True}),
#             },
#         }

#     RETURN_TYPES = ("GUIDER",)

#     FUNCTION = "get_guider_mixer"
#     CATEGORY = "QS/Samplers"
# #cfg
#     def get_guider_mixer(self, g0, positive, negative,  g1=None, g2=None, g3=None, model_mixing_steps='{"g0":[], "remaining_steps":"g0"}'):
#         guider = QSCFGMixerGuider(g0, g1, g2, g3, model_mixing_steps)
#         guider.set_conds(positive, negative)
#         guider.set_cfg(g0.cfg)
#         return (guider,)

# class QSInpaintGuider(QSCFGGuider):
#     # def __init__(self, model_patcher, **kwargs):
#     #     super().__init__(model_patcher, **kwargs)
    
#     def pre_sample(self, noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed):
#         [self.original_latents, self.motion_mask_latents, self.noise_generator] = [self.kwargs.get(k, None) for k in ["original_latents", "motion_mask_latents","noise_generator"]]
#         self.original_latents = self.original_latents['samples'].to(noise.device) if self.original_latents is not None else None
#         self.motion_mask_latents = self.motion_mask_latents['samples'].to(noise.device) if self.motion_mask_latents is not None else None
#         return super().pre_sample(noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed)
    
#     def post_sample(self, noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed, output):
#         [self.original_latents, self.motion_mask_latents, self.noise, self.noise_generator] = [None, None, None, None]
#         return super().post_sample(noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed, output)
    
#     def predict_noise(self, x, timestep, model_options={}, seed=None):
#         if self.motion_mask_latents is None or self.original_latents is None or self.noise_generator is None:
#             return comfy.samplers.sampling_function(
#                 self.inner_model, x, timestep, self.conds.get("negative", None),
#                 self.conds.get("positive", None), self.cfg, model_options=model_options, seed=seed
#             )
            
#         sigma = timestep.clone()
#         latent_mask = (1. - self.motion_mask_latents).to(x.device)
#         current_noise = self.noise_generator.generate_noise({"samples": self.motion_mask_latents}).to(x.device)
#             # original_latents_with_noise = self.inner_model.model_sampling.noise_scaling( sigma.reshape([sigma.shape[0]] + [1] * (len(current_noise.shape) - 1)), current_noise, original_latents).to(x.device)
#             #if this is QS inpaiting go ahaed and modify the latents here
#         current_sigma = (sigma.reshape([sigma.shape[0]] + [1] * (len(current_noise.shape) - 1))).to(x.device)
#         original_latents_with_noise = (self.original_latents * (1 - current_sigma) + current_noise * current_sigma).to(x.device)
        
#         x = x * self.motion_mask_latents + original_latents_with_noise * latent_mask
        
#         return comfy.samplers.sampling_function(
#             self.inner_model, x, timestep, self.conds.get("negative", None),
#             self.conds.get("positive", None), self.cfg, model_options=model_options, seed=seed
#         )


# class QSSaveImage:
#     def __init__(self):
#         self.output_dir = folder_paths.get_input_directory()
#         self.type = "input"
#         self.prefix_append = ""
#         self.compress_level = 4

#     @classmethod
#     def INPUT_TYPES(s):
#         return {
#             "required": {
#                 "images": ("IMAGE", {"tooltip": "The images to save."}),
#                 "filename_prefix": ("STRING", {"default": "ComfyUI", "tooltip": "The prefix for the file to save. This may include formatting information such as %date:yyyy-MM-dd% or %Empty Latent Image.width% to include values from nodes."})
#             },
#             "hidden": {
#                 "prompt": "PROMPT", "extra_pnginfo": "EXTRA_PNGINFO"
#             },
#         }

#     RETURN_TYPES = ("STRING",)
#     RETURN_NAMES = ("filename",)
#     FUNCTION = "save_images"

#     OUTPUT_NODE = True

#     CATEGORY = "QS"
#     DESCRIPTION = "Saves the input images to your ComfyUI input directory."

#     def save_images(self, images, filename_prefix="ComfyUI", prompt=None, extra_pnginfo=None):
#         filename_prefix += self.prefix_append
#         full_output_folder, filename, counter, subfolder, filename_prefix = folder_paths.get_save_image_path(filename_prefix, self.output_dir, images[0].shape[1], images[0].shape[0])
#         results = list()
#         for (batch_number, image) in enumerate(images):
#             i = 255. * image.cpu().numpy()
#             img = Image.fromarray(numpy.clip(i, 0, 255).astype(numpy.uint8))
#             metadata = None
#             if not args.disable_metadata:
#                 metadata = PngInfo()
#                 if prompt is not None:
#                     metadata.add_text("prompt", json.dumps(prompt))
#                 if extra_pnginfo is not None:
#                     for x in extra_pnginfo:
#                         metadata.add_text(x, json.dumps(extra_pnginfo[x]))

#             filename_with_batch_num = filename.replace("%batch_num%", str(batch_number))
#             file = f"{filename_with_batch_num}_{counter:05}_.png"
#             img.save(os.path.join(full_output_folder, file), pnginfo=metadata, compress_level=self.compress_level)
#             results.append({
#                 "filename": file,
#                 "subfolder": subfolder,
#                 "type": self.type
#             })
#             counter += 1

#         return { "ui": { "images": results }, "result": (file,) }

# class QSCFGGuider:
#     def __init__(self, model_patcher, **kwargs):
#         self.model_patcher = model_patcher
#         self.model_options = model_patcher.model_options
#         self.original_conds = {}
#         self.cfg = 1.0
#         self.kwargs = kwargs # just assign any additional params,  the derived class might use it
#         # for k in kwargs:
#         #     setattr(self, k, kwargs[k])
#         self.process_additional_params(model_patcher, **kwargs)
        
#     #following methods can be overriden in derived class
#     def process_additional_params(self, model_patcher, **kwargs):
#         pass
    
#     def pre_sample(self, noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed):
#         pass
#     def post_sample(self, noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed, output):
#         return output
        
#     def set_conds(self, positive, negative, **kwargs):
#         self.inner_set_conds({"positive": positive, "negative": negative})

#     def set_cfg(self, cfg, **kwargs):
#         self.cfg = cfg

#     def inner_set_conds(self, conds):
#         for k in conds:
#             self.original_conds[k] = comfy.sampler_helpers.convert_cond(conds[k])

#     def __call__(self, *args, **kwargs):
#         return self.predict_noise(*args, **kwargs)

#     def predict_noise(self, x, timestep, model_options={}, seed=None):
#         return comfy.samplers.sampling_function(self.inner_model, x, timestep, self.conds.get("negative", None), self.conds.get("positive", None), self.cfg, model_options=model_options, seed=seed)

#     def inner_sample(self, noise, latent_image, device, sampler, sigmas, denoise_mask, callback, disable_pbar, seed):
#         if latent_image is not None and torch.count_nonzero(latent_image) > 0: #Don't shift the empty latent image.
#             latent_image = self.inner_model.process_latent_in(latent_image)

#         self.conds = comfy.samplers.process_conds(self.inner_model, noise, self.conds, device, latent_image, denoise_mask, seed)

#         extra_args = {"model_options": self.model_options, "seed":seed}

#         samples = sampler.sample(self, sigmas, extra_args, callback, noise, latent_image, denoise_mask, disable_pbar)
#         return self.inner_model.process_latent_out(samples.to(torch.float32))

#     def sample(self, noise, latent_image, sampler, sigmas, denoise_mask=None, callback=None, disable_pbar=False, seed=None):
#         if sigmas.shape[-1] == 0:
#             return latent_image

#         self.conds = {}
#         for k in self.original_conds:
#             self.conds[k] = list(map(lambda a: a.copy(), self.original_conds[k]))

#         self.inner_model, self.conds, self.loaded_models = comfy.sampler_helpers.prepare_sampling(self.model_patcher, noise.shape, self.conds)
#         device = self.model_patcher.load_device

#         if denoise_mask is not None:
#             denoise_mask = comfy.sampler_helpers.prepare_mask(denoise_mask, noise.shape, device)

#         noise = noise.to(device)
#         latent_image = latent_image.to(device)
#         sigmas = sigmas.to(device)
#         self.sigmas = sigmas
#         self.latent_image = latent_image
#         self.noise = noise
#         self.pre_sample(noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed)
#         output = self.inner_sample(noise, latent_image, device, sampler, sigmas, denoise_mask, callback, disable_pbar, seed)
#         output = self.post_sample(noise, latent_image, sampler, sigmas, denoise_mask, callback, disable_pbar, seed, output)

#         comfy.sampler_helpers.cleanup_models(self.conds, self.loaded_models)
#         del self.inner_model
#         del self.conds
#         del self.loaded_models
#         return output

# class QSCFGGuiderNode:
#     @classmethod
#     def INPUT_TYPES(s):
#         return {"required":
#                     {"model": ("MODEL",),
#                     "positive": ("CONDITIONING", ),
#                     "negative": ("CONDITIONING", ),
#                     "cfg": ("FLOAT", {"default": 8.0, "min": 0.0, "max": 100.0, "step":0.1, "round": 0.01}),
#                      }
#                 }

#     RETURN_TYPES = ("GUIDER",)

#     FUNCTION = "get_guider"
#     CATEGORY = "QS/samplers"

#     def get_guider(self, model, positive, negative, cfg, **kwargs):
#         guider = QSCFGGuider(model, **kwargs)
#         guider.set_conds(positive, negative,**kwargs)
#         guider.set_cfg(cfg, **kwargs)
#         return (guider,)




# class QSInpaintGuiderNode:
#     @classmethod
#     def INPUT_TYPES(s):
#         return {
#             "required": {
#                 "guider": ("GUIDER",),
#                 "positive": ("CONDITIONING",),
#                 "negative": ("CONDITIONING",),
#        #         "cfg": ("FLOAT", {"default": 8.0, "min": 0.0, "max": 100.0, "step": 0.1, "round": 0.01}),
#                 "noise_generator": ("NOISE",),
#             },
#             "optional": {
#                 "original_latents": ("LATENT",),
#                 "motion_mask_latents": ("LATENT",),
#             },
#         }

#     RETURN_TYPES = ("GUIDER",)

#     FUNCTION = "get_guider_mixer"
#     CATEGORY = "QS/Samplers"
# #cfg
#     def get_guider_mixer(self, guider, positive, negative,  noise_generator, original_latents=None, motion_mask_latents=None):
#         guider1 = QSInpaintGuider(guider.model_patcher, noise_generator=noise_generator, original_latents=original_latents, motion_mask_latents=motion_mask_latents)
#         guider1.set_conds(positive, negative)
#         guider1.set_cfg(guider.cfg)
#         return (guider1,)

