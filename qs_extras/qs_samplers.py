
import comfy.samplers
from nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
class QSInpaintCFGGuider(comfy.samplers.CFGGuider):
    def __init__(self, model_patcher, noise_generator=None, original_latents=None, motion_mask_latents=None,
                 sigmas=None, start_step=0, end_step=0):
        super().__init__(model_patcher)
        self.noise_generator = noise_generator
        self.original_latents = original_latents
        self.motion_mask_latents = motion_mask_latents
        self.sigmas = sigmas
        self.start_step = start_step
        self.end_step = end_step

    def set_conds(self, positive, negative):
        conds = {} 
        if positive :
            conds["positive"] = positive
        if negative :
            conds["negative"] = negative
        self.inner_set_conds(conds)

    SERVER_ONLY=True

    def predict_noise(self, x, timestep, model_options={}, seed=None):
        if self.motion_mask_latents is None or self.original_latents is None or self.noise_generator is None:
            return comfy.samplers.sampling_function(
                self.inner_model, x, timestep, self.conds.get("negative", None),
                self.conds.get("positive", None), self.cfg, model_options=model_options, seed=seed
            )
        original_latents = self.original_latents['samples'].to(x.device)
        motion_mask_latents = self.motion_mask_latents['samples'].to(x.device)
        sigma = timestep.clone()
        latent_mask = (1. - motion_mask_latents).to(x.device)
        current_noise = self.noise_generator.generate_noise({"samples": motion_mask_latents}).to(x.device)
            # original_latents_with_noise = self.inner_model.model_sampling.noise_scaling( sigma.reshape([sigma.shape[0]] + [1] * (len(current_noise.shape) - 1)), current_noise, original_latents).to(x.device)
            #if this is QS inpaiting go ahaed and modify the latents here
        current_sigma = (sigma.reshape([sigma.shape[0]] + [1] * (len(current_noise.shape) - 1))).to(x.device)
        original_latents_with_noise = (original_latents * (1 - current_sigma) + current_noise * current_sigma).to(x.device)
        
        x = x * motion_mask_latents + original_latents_with_noise * latent_mask
        
        return comfy.samplers.sampling_function(
            self.inner_model, x, timestep, self.conds.get("negative", None),
            self.conds.get("positive", None), self.cfg, model_options=model_options, seed=seed
        )
    
    

class QSInpaintCFGGuiderNode:
    @classmethod
    def INPUT_TYPES(cls):
        return {"required":
                    {"model": ("MODEL",),
                     },
                    "optional": {
                    "positive": ("CONDITIONING", {"default": None}),
                    "negative": ("CONDITIONING", {"default": None}),
                    "cfg": ("FLOAT", {"default": 8.0, "min": 0.0, "max": 100.0, "step":0.1, "round": 0.01}),
                    "noise_generator": ("NOISE",{"default": None}),
                    "original_latents": ("LATENT",{"default": None}),
                    "motion_mask_latents": ("LATENT",{"default": None}),
                    "sigmas": ("SIGMAS",{"default": None} ),
                    "start_step": ("INT", {"default": 0}),
                    "end_step": ("INT", {"default": 0}),
                    }
                }

    RETURN_TYPES = ("GUIDER",)

    FUNCTION = "get_guider"
    CATEGORY = "QS/samplers"
    SERVER_ONLY=True

    def get_guider(self, model, positive=None, negative=None, cfg=8.0, 
                   noise_generator=None, original_latents=None, motion_mask_latents=None, sigmas=None, start_step=0, end_step=0):
        guider = QSInpaintCFGGuider(model, noise_generator, original_latents, motion_mask_latents, sigmas, start_step, end_step)
        guider.set_conds(positive, negative)
        guider.set_cfg(cfg)
        return (guider,)

    
NODE_CLASS_MAPPINGS = {
    "QSInpaintCFGGuiderNode": QSInpaintCFGGuiderNode,
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "QSInpaintCFGGuiderNode" : "QS Inpaint CFG Guider"
}