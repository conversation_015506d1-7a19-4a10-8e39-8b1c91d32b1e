import torch

class QSMaskPaddingNode:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "mask": ("MASK", ),
                "left": ("INT", {"default": 10, "min": 0, "step":5}),
                "top": ("INT", {"default": 10, "min": 0,"step":5}),
                "right": ("INT", {"default": 10, "min": 0,"step":5}),
                "bottom": ("INT", {"default": 10, "min": 0,"step":5}),
            }
        }

    RETURN_TYPES = ("MASK",)
    FUNCTION = "apply_padding"
    CATEGORY = "QS/mask"

    def apply_padding(self, mask, left, top, right, bottom):
        device = mask.device  # Maintain same device
        batch, h, w = mask.shape  # Get batch size, height, and width
        padded_masks = torch.zeros_like(mask, device=device)  # Initialize output masks

        for i in range(batch):
            single_mask = mask[i]  # Process each mask individually
            coords = torch.nonzero(single_mask)

            if coords.numel() == 0:
                padded_masks[i] = single_mask  # If no white region, keep original
                continue

            y_min, x_min = coords[:, 0].min(), coords[:, 1].min()
            y_max, x_max = coords[:, 0].max(), coords[:, 1].max()

            # Apply padding with boundary constraints
            y_min = max(y_min - top, 0)
            x_min = max(x_min - left, 0)
            y_max = min(y_max + bottom, h - 1)
            x_max = min(x_max + right, w - 1)

            # Assign padded region to output mask
            padded_masks[i, y_min:y_max + 1, x_min:x_max + 1] = 1

        return (padded_masks,)


NODE_CLASS_MAPPINGS = {
    "QSMaskPaddingNode": QSMaskPaddingNode,
}


NODE_DISPLAY_NAME_MAPPINGS = {
    "QSMaskPaddingNode": "QS Mask Padding",
}
