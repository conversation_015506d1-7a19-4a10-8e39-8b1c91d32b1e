Directory            Nodes                                             
======================================================================
.                    "SaveImageWebsocket"
------------------------------------
ComfyLiterals        "Int", "Float", "String"
                     "KepStringLiteral", "Operation", "Checkpoint"
                     "Lora"
------------------------------------
ComfyMath            "CM_BoolUnaryOperation", "CM_BoolBinaryOperation", "CM_BoolToInt"
                     "CM_IntToBool", "CM_FloatToInt", "CM_IntToFloat"
                     "CM_IntToNumber", "CM_NumberToInt", "CM_FloatToNumber"
                     "CM_NumberToFloat", "CM_ComposeVec2", "CM_ComposeVec3"
                     "CM_ComposeVec4", "CM_BreakoutVec2", "CM_BreakoutVec3"
                     "CM_BreakoutVec4", "CM_FloatUnaryOperation", "CM_FloatUnaryCondition"
                     "CM_FloatBinaryOperation", "CM_FloatBinaryCondition", "CM_SDXLResolution"
                     "CM_NearestSDXLResolution", "CM_IntUnaryOperation", "CM_IntUnaryCondition"
                     "CM_IntBinaryOperation", "CM_IntBinaryCondition", "CM_NumberUnaryOperation"
                     "CM_NumberUnaryCondition", "CM_NumberBinaryOperation", "CM_NumberBinaryCondition"
                     "CM_Vec2UnaryOperation", "CM_Vec2UnaryCondition", "CM_Vec2ToScalarUnaryOperation"
                     "CM_Vec2BinaryOperation", "CM_Vec2BinaryCondition", "CM_Vec2ToScalarBinaryOperation"
                     "CM_Vec2ScalarOperation", "CM_Vec3UnaryOperation", "CM_Vec3UnaryCondition"
                     "CM_Vec3ToScalarUnaryOperation", "CM_Vec3BinaryOperation", "CM_Vec3BinaryCondition"
                     "CM_Vec3ToScalarBinaryOperation", "CM_Vec3ScalarOperation", "CM_Vec4UnaryOperation"
                     "CM_Vec4UnaryCondition", "CM_Vec4ToScalarUnaryOperation", "CM_Vec4BinaryOperation"
                     "CM_Vec4BinaryCondition", "CM_Vec4ToScalarBinaryOperation", "CM_Vec4ScalarOperation"
------------------------------------
ComfyUI-Advanced-ControlNet "TimestepKeyframe", "ACN_TimestepKeyframeInterpolation", "ACN_TimestepKeyframeFromStrengthList"
                     "LatentKeyframe", "LatentKeyframeTiming", "LatentKeyframeBatchedGroup"
                     "LatentKeyframeGroup", "ACN_AdvancedControlNetApply", "ControlNetLoaderAdvanced"
                     "DiffControlNetLoaderAdvanced", "ScaledSoftControlNetWeights", "ScaledSoftMaskedUniversalWeights"
                     "SoftControlNetWeights", "CustomControlNetWeights", "SoftT2IAdapterWeights"
                     "CustomT2IAdapterWeights", "ACN_DefaultUniversalWeights", "ACN_SparseCtrlRGBPreprocessor"
                     "ACN_SparseCtrlLoaderAdvanced", "ACN_SparseCtrlMergedLoaderAdvanced", "ACN_SparseCtrlIndexMethodNode"
                     "ACN_SparseCtrlSpreadMethodNode", "ACN_SparseCtrlWeightExtras", "ACN_ControlNet++LoaderSingle"
                     "ACN_ControlNet++LoaderAdvanced", "ACN_ControlNet++InputNode", "ACN_ReferencePreprocessor"
                     "ACN_ReferenceControlNet", "ACN_ReferenceControlNetFinetune", "LoadImagesFromDirectory"
------------------------------------
ComfyUI-Allor        "AlphaChanelAdd", "AlphaChanelAddByMask", "AlphaChanelAsMask"
                     "AlphaChanelRestore", "AlphaChanelRemove", "ClipClamp"
                     "ClipVisionClamp", "ClipVisionOutputClamp", "ConditioningClamp"
                     "ControlNetClamp", "GligenClamp", "ImageClamp"
                     "LatentClamp", "MaskClamp", "ModelClamp"
                     "StyleModelClamp", "UpscaleModelClamp", "VaeClamp"
                     "ImageBatchGet", "ImageBatchCopy", "ImageBatchRemove"
                     "ImageBatchFork", "ImageBatchJoin", "ImageBatchPermute"
                     "ImageCompositeAbsolute", "ImageCompositeAbsoluteByContainer", "ImageCompositeRelative"
                     "ImageCompositeRelativeByContainer", "ImageContainer", "ImageContainerInheritanceAdd"
                     "ImageContainerInheritanceScale", "ImageContainerInheritanceMax", "ImageContainerInheritanceSum"
                     "ImageDrawArc", "ImageDrawArcByContainer", "ImageDrawChord"
                     "ImageDrawChordByContainer", "ImageDrawEllipse", "ImageDrawEllipseByContainer"
                     "ImageDrawLine", "ImageDrawLineByContainer", "ImageDrawPieslice"
                     "ImageDrawPiesliceByContainer", "ImageDrawRectangle", "ImageDrawRectangleByContainer"
                     "ImageDrawRectangleRounded", "ImageDrawRectangleRoundedByContainer", "ImageDrawPolygon"
                     "ImageEffectsAdjustment", "ImageEffectsGrayscale", "ImageEffectsNegative"
                     "ImageEffectsSepia", "ImageEffectsLensZoomBurst", "ImageEffectsLensChromaticAberration"
                     "ImageEffectsLensBokeh", "ImageEffectsLensOpticAxis", "ImageEffectsLensVignette"
                     "ImageFilterSmooth", "ImageFilterSmoothMore", "ImageFilterBlur"
                     "ImageFilterBoxBlur", "ImageFilterGaussianBlur", "ImageFilterGaussianBlurAdvanced"
                     "ImageFilterStackBlur", "ImageFilterMedianBlur", "ImageFilterBilateralBlur"
                     "ImageFilterContour", "ImageFilterDetail", "ImageFilterEdgeEnhance"
                     "ImageFilterEdgeEnhanceMore", "ImageFilterEmboss", "ImageFilterFindEdges"
                     "ImageFilterSharpen", "ImageFilterRank", "ImageFilterMin"
                     "ImageFilterMax", "ImageFilterMode", "ImageNoiseBeta"
                     "ImageNoiseBinomial", "ImageNoiseBytes", "ImageNoiseGaussian"
                     "ImageSegmentation", "ImageSegmentationCustom", "ImageSegmentationCustomAdvanced"
                     "ImageText", "ImageTextOutlined", "ImageTextMultiline"
                     "ImageTextMultilineOutlined", "ImageTransformResizeAbsolute", "ImageTransformResizeRelative"
                     "ImageTransformResizeClip", "ImageTransformCropAbsolute", "ImageTransformCropRelative"
                     "ImageTransformCropCorners", "ImageTransformPaddingAbsolute", "ImageTransformPaddingRelative"
                     "ImageTransformRotate", "ImageTransformTranspose"
------------------------------------
ComfyUI-BRIA_AI-RMBG "BRIA_RMBG_ModelLoader_Zho", "BRIA_RMBG_Zho"
------------------------------------
ComfyUI-Custom-Scripts "LoraLoader|pysssss", "CheckpointLoader|pysssss", "ConstrainImage|pysssss"
                     "ConstrainImageforVideo|pysssss", "MathExpression|pysssss", "PlaySound|pysssss"
                     "Repeater|pysssss", "ReroutePrimitive|pysssss", "ShowText|pysssss"
                     "StringFunction|pysssss", "SystemNotification|pysssss", "LoadText|pysssss"
                     "SaveText|pysssss"
------------------------------------
ComfyUI-Doubutsu-Describer "DoubutsuDescriber"
------------------------------------
ComfyUI-DynamiCrafterWrapper "QSMotionMasks", "DynamiCrafterI2V", "DynamiCrafterBatchInterpolation"
                     "DownloadAndLoadDynamiCrafterModel", "DownloadAndLoadCLIPModel", "DownloadAndLoadCLIPVisionModel"
                     "DynamiCrafterLoadInitNoise", "DownloadAndLoadDynamiCrafterCNModel", "DynamiCrafterControlnetApply"
                     "LoadFreeNoiseModel", "FreeNoiseDynamicI2V", "QSMotionMasks"
                     "DynamiCrafterI2V", "DynamiCrafterBatchInterpolation", "DownloadAndLoadDynamiCrafterModel"
                     "DownloadAndLoadCLIPModel", "DownloadAndLoadCLIPVisionModel", "DynamiCrafterLoadInitNoise"
                     "DownloadAndLoadDynamiCrafterCNModel", "DynamiCrafterControlnetApply", "LoadFreeNoiseModel"
                     "FreeNoiseDynamicI2V"
------------------------------------
ComfyUI-Easy-Use     "easy seed", "easy globalSeed", "easy positive"
                     "easy negative", "easy wildcards", "easy prompt"
                     "easy promptList", "easy promptLine", "easy promptConcat"
                     "easy promptReplace", "easy stylesSelector", "easy portraitMaster"
                     "easy fullLoader", "easy a1111Loader", "easy comfyLoader"
                     "easy hunyuanDiTLoader", "easy svdLoader", "easy sv3dLoader"
                     "easy zero123Loader", "easy dynamiCrafterLoader", "easy cascadeLoader"
                     "easy kolorsLoader", "easy fluxLoader", "easy pixArtLoader"
                     "easy loraStack", "easy controlnetStack", "easy controlnetLoader"
                     "easy controlnetLoaderADV", "easy controlnetLoader++", "easy LLLiteLoader"
                     "easy loraStackApply", "easy controlnetStackApply", "easy ipadapterApply"
                     "easy ipadapterApplyADV", "easy ipadapterApplyFaceIDKolors", "easy ipadapterApplyEncoder"
                     "easy ipadapterApplyEmbeds", "easy ipadapterApplyRegional", "easy ipadapterApplyFromParams"
                     "easy ipadapterStyleComposition", "easy instantIDApply", "easy instantIDApplyADV"
                     "easy pulIDApply", "easy pulIDApplyADV", "easy styleAlignedBatchAlign"
                     "easy icLightApply", "easy applyFooocusInpaint", "easy applyBrushNet"
                     "easy applyPowerPaint", "easy applyInpaint", "easy latentNoisy"
                     "easy latentCompositeMaskedWithCond", "easy injectNoiseToLatent", "easy preSampling"
                     "easy preSamplingAdvanced", "easy preSamplingNoiseIn", "easy preSamplingCustom"
                     "easy preSamplingSdTurbo", "easy preSamplingDynamicCFG", "easy preSamplingCascade"
                     "easy preSamplingLayerDiffusion", "easy preSamplingLayerDiffusionADDTL", "easy fullkSampler"
                     "easy kSampler", "easy kSamplerCustom", "easy kSamplerTiled"
                     "easy kSamplerLayerDiffusion", "easy kSamplerInpainting", "easy kSamplerDownscaleUnet"
                     "easy kSamplerSDTurbo", "easy fullCascadeKSampler", "easy cascadeKSampler"
                     "easy unSampler", "easy hiresFix", "easy preDetailerFix"
                     "easy preMaskDetailerFix", "easy ultralyticsDetectorPipe", "easy samLoaderPipe"
                     "easy detailerFix", "easy pipeIn", "easy pipeOut"
                     "easy pipeEdit", "easy pipeEditPrompt", "easy pipeToBasicPipe"
                     "easy pipeBatchIndex", "easy XYPlot", "easy XYPlotAdvanced"
                     "easy XYInputs: Seeds++ Batch", "easy XYInputs: Steps", "easy XYInputs: CFG Scale"
                     "easy XYInputs: Sampler/Scheduler", "easy XYInputs: Denoise", "easy XYInputs: Checkpoint"
                     "easy XYInputs: Lora", "easy XYInputs: ModelMergeBlocks", "easy XYInputs: PromptSR"
                     "easy XYInputs: ControlNet", "easy XYInputs: PositiveCond", "easy XYInputs: PositiveCondList"
                     "easy XYInputs: NegativeCond", "easy XYInputs: NegativeCondList", "easy showSpentTime"
                     "easy showLoaderSettingsNames", "easy sliderControl", "dynamicThresholdingFull"
                     "easy stableDiffusion3API", "easy ckptNames", "easy controlnetNames"
                     "easy imageInsetCrop", "easy imageCount", "easy imageSize"
                     "easy imageSizeBySide", "easy imageSizeByLongerSide", "easy imagePixelPerfect"
                     "easy imageScaleDown", "easy imageScaleDownBy", "easy imageScaleDownToSize"
                     "easy imageScaleToNormPixels", "easy imageRatio", "easy imageConcat"
                     "easy imageListToImageBatch", "easy imageBatchToImageList", "easy imageSplitList"
                     "easy imageSplitGrid", "easy imagesSplitImage", "easy imageSplitTiles"
                     "easy imageCropFromMask", "easy imageUncropFromBBOX", "easy imageSave"
                     "easy imageRemBg", "easy imageChooser", "easy imageColorMatch"
                     "easy imageDetailTransfer", "easy imageInterrogator", "easy loadImageBase64"
                     "easy imageToBase64", "easy joinImageBatch", "easy humanSegmentation"
                     "easy removeLocalImage", "easy loadImagesForLoop", "easy string"
                     "easy int", "easy rangeInt", "easy float"
                     "easy rangeFloat", "easy boolean", "easy mathString"
                     "easy mathInt", "easy mathFloat", "easy compare"
                     "easy imageSwitch", "easy textSwitch", "easy imageIndexSwitch"
                     "easy textIndexSwitch", "easy conditioningIndexSwitch", "easy anythingIndexSwitch"
                     "easy ab", "easy anythingInversedSwitch", "easy whileLoopStart"
                     "easy whileLoopEnd", "easy forLoopStart", "easy forLoopEnd"
                     "easy blocker", "easy ifElse", "easy isNone"
                     "easy isSDXL", "easy isFileExist", "easy outputToList"
                     "easy pixels", "easy xyAny", "easy lengthAnything"
                     "easy batchAnything", "easy convertAnything", "easy showAnything"
                     "easy showAnythingLazy", "easy showTensorShape", "easy clearCacheKey"
                     "easy clearCacheAll", "easy cleanGpuUsed", "easy saveText"
                     "easy saveTextLazy", "easy if", "easy poseEditor"
                     "easy imageToMask"
------------------------------------
ComfyUI-Florence-2   "LoadFlorence2Model", "Florence2", "Florence2Postprocess"
                     "Florence2PostprocessAll"
------------------------------------
ComfyUI-Florence2    "DownloadAndLoadFlorence2Model", "DownloadAndLoadFlorence2Lora", "Florence2ModelLoader"
                     "Florence2Run"
------------------------------------
ComfyUI-Frame-Interpolation "KSampler Gradually Adding More Denoise (efficient)", "GMFSS Fortuna VFI", "IFRNet VFI"
                     "IFUnet VFI", "M2M VFI", "RIFE VFI"
                     "Sepconv VFI", "AMT VFI", "FILM VFI"
                     "Make Interpolation State List", "STMFNet VFI", "FLAVR VFI"
                     "CAIN VFI", "VFI FloatToInt"
------------------------------------
ComfyUI-GGUF         "UnetLoaderGGUF", "CLIPLoaderGGUF", "DualCLIPLoaderGGUF"
                     "TripleCLIPLoaderGGUF", "UnetLoaderGGUFAdvanced"
------------------------------------
ComfyUI-IC-Light     "LoadAndApplyICLightUnet", "ICLightConditioning", "LightSource"
                     "CalculateNormalsFromImages", "LoadHDRImage", "BackgroundScaler"
                     "DetailTransfer"
------------------------------------
ComfyUI-Impact-Pack  "SAMLoader", "CLIPSegDetectorProvider", "ONNXDetectorProvider"
                     "BitwiseAndMaskForEach", "SubtractMaskForEach", "DetailerForEach"
                     "DetailerForEachDebug", "DetailerForEachPipe", "DetailerForEachDebugPipe"
                     "DetailerForEachPipeForAnimateDiff", "SAMDetectorCombined", "SAMDetectorSegmented"
                     "FaceDetailer", "FaceDetailerPipe", "MaskDetailerPipe"
                     "ToDetailerPipe", "ToDetailerPipeSDXL", "FromDetailerPipe"
                     "FromDetailerPipe_v2", "FromDetailerPipeSDXL", "ToBasicPipe"
                     "FromBasicPipe", "FromBasicPipe_v2", "BasicPipeToDetailerPipe"
                     "BasicPipeToDetailerPipeSDXL", "DetailerPipeToBasicPipe", "EditBasicPipe"
                     "EditDetailerPipe", "EditDetailerPipeSDXL", "LatentPixelScale"
                     "PixelKSampleUpscalerProvider", "PixelKSampleUpscalerProviderPipe", "IterativeLatentUpscale"
                     "IterativeImageUpscale", "PixelTiledKSampleUpscalerProvider", "PixelTiledKSampleUpscalerProviderPipe"
                     "TwoSamplersForMaskUpscalerProvider", "TwoSamplersForMaskUpscalerProviderPipe", "PixelKSampleHookCombine"
                     "DenoiseScheduleHookProvider", "StepsScheduleHookProvider", "CfgScheduleHookProvider"
                     "NoiseInjectionHookProvider", "UnsamplerHookProvider", "CoreMLDetailerHookProvider"
                     "PreviewDetailerHookProvider", "DetailerHookCombine", "NoiseInjectionDetailerHookProvider"
                     "UnsamplerDetailerHookProvider", "DenoiseSchedulerDetailerHookProvider", "SEGSOrderedFilterDetailerHookProvider"
                     "SEGSRangeFilterDetailerHookProvider", "SEGSLabelFilterDetailerHookProvider", "VariationNoiseDetailerHookProvider"
                     "BitwiseAndMask", "SubtractMask", "AddMask"
                     "ImpactSegsAndMask", "ImpactSegsAndMaskForEach", "EmptySegs"
                     "MediaPipeFaceMeshToSEGS", "MaskToSEGS", "MaskToSEGS_for_AnimateDiff"
                     "ToBinaryMask", "MasksToMaskList", "MaskListToMaskBatch"
                     "ImageListToImageBatch", "SetDefaultImageForSEGS", "RemoveImageFromSEGS"
                     "BboxDetectorSEGS", "SegmDetectorSEGS", "ONNXDetectorSEGS"
                     "ImpactSimpleDetectorSEGS_for_AD", "ImpactSimpleDetectorSEGS", "ImpactSimpleDetectorSEGSPipe"
                     "ImpactControlNetApplySEGS", "ImpactControlNetApplyAdvancedSEGS", "ImpactControlNetClearSEGS"
                     "ImpactIPAdapterApplySEGS", "ImpactDecomposeSEGS", "ImpactAssembleSEGS"
                     "ImpactFrom_SEG_ELT", "ImpactEdit_SEG_ELT", "ImpactDilate_Mask_SEG_ELT"
                     "ImpactDilateMask", "ImpactGaussianBlurMask", "ImpactDilateMaskInSEGS"
                     "ImpactGaussianBlurMaskInSEGS", "ImpactScaleBy_BBOX_SEG_ELT", "ImpactFrom_SEG_ELT_bbox"
                     "ImpactFrom_SEG_ELT_crop_region", "ImpactCount_Elts_in_SEGS", "BboxDetectorCombined_v2"
                     "SegmDetectorCombined_v2", "SegsToCombinedMask", "KSamplerProvider"
                     "TwoSamplersForMask", "TiledKSamplerProvider", "KSamplerAdvancedProvider"
                     "TwoAdvancedSamplersForMask", "ImpactNegativeConditioningPlaceholder", "PreviewBridge"
                     "PreviewBridgeLatent", "ImageSender", "ImageReceiver"
                     "LatentSender", "LatentReceiver", "ImageMaskSwitch"
                     "LatentSwitch", "SEGSSwitch", "ImpactSwitch"
                     "ImpactInversedSwitch", "ImpactWildcardProcessor", "ImpactWildcardEncode"
                     "SEGSUpscaler", "SEGSUpscalerPipe", "SEGSDetailer"
                     "SEGSPaste", "SEGSPreview", "SEGSPreviewCNet"
                     "SEGSToImageList", "ImpactSEGSToMaskList", "ImpactSEGSToMaskBatch"
                     "ImpactSEGSConcat", "ImpactSEGSPicker", "ImpactMakeTileSEGS"
                     "SEGSDetailerForAnimateDiff", "ImpactKSamplerBasicPipe", "ImpactKSamplerAdvancedBasicPipe"
                     "ReencodeLatent", "ReencodeLatentPipe", "ImpactImageBatchToImageList"
                     "ImpactMakeImageList", "ImpactMakeImageBatch", "RegionalSampler"
                     "RegionalSamplerAdvanced", "CombineRegionalPrompts", "RegionalPrompt"
                     "ImpactCombineConditionings", "ImpactConcatConditionings", "ImpactSEGSLabelAssign"
                     "ImpactSEGSLabelFilter", "ImpactSEGSRangeFilter", "ImpactSEGSOrderedFilter"
                     "ImpactCompare", "ImpactConditionalBranch", "ImpactConditionalBranchSelMode"
                     "ImpactIfNone", "ImpactConvertDataType", "ImpactLogicalOperators"
                     "ImpactInt", "ImpactFloat", "ImpactValueSender"
                     "ImpactValueReceiver", "ImpactImageInfo", "ImpactLatentInfo"
                     "ImpactMinMax", "ImpactNeg", "ImpactConditionalStopIteration"
                     "ImpactStringSelector", "StringListToString", "WildcardPromptFromString"
                     "RemoveNoiseMask", "ImpactLogger", "ImpactDummyInput"
                     "ImpactQueueTrigger", "ImpactQueueTriggerCountdown", "ImpactSetWidgetValue"
                     "ImpactNodeSetMuteState", "ImpactControlBridge", "ImpactIsNotEmptySEGS"
                     "ImpactSleep", "ImpactRemoteBoolean", "ImpactRemoteInt"
                     "ImpactHFTransformersClassifierProvider", "ImpactSEGSClassify", "ImpactSchedulerAdapter"
                     "GITSSchedulerFuncProvider", "UltralyticsDetectorProvider"
------------------------------------
ComfyUI-Inpaint-CropAndStitch "InpaintCrop", "InpaintStitch", "InpaintExtendOutpaint"
                     "InpaintResize"
------------------------------------
ComfyUI-Marigold     "MarigoldModelLoader", "MarigoldDepthEstimation_v2", "MarigoldDepthEstimation_v2_video"
                     "MarigoldDepthEstimation", "MarigoldDepthEstimationVideo", "ColorizeDepthmap"
                     "SaveImageOpenEXR", "RemapDepth", "MarigoldDepthEstimation"
                     "MarigoldDepthEstimationVideo", "ColorizeDepthmap", "SaveImageOpenEXR"
                     "RemapDepth", "MarigoldModelLoader", "MarigoldDepthEstimation_v2"
                     "MarigoldDepthEstimation_v2_video"
------------------------------------
ComfyUI-Ollama-Describer "OllamaImageDescriber", "OllamaTextDescriber", "TextTransformer"
                     "InputText"
------------------------------------
ComfyUI-SAM2         "SAM2ModelLoader (segment anything)", "GroundingDinoModelLoader (segment anything)", "GroundingDinoSAMSegment (segment anything)"
                     "InvertMask (segment anything)", "IsMaskEmpty"
------------------------------------
ComfyUI-SUPIR        "SUPIR_Upscale", "SUPIR_sample", "SUPIR_model_loader"
                     "SUPIR_first_stage", "SUPIR_encode", "SUPIR_decode"
                     "SUPIR_conditioner", "SUPIR_tiles", "SUPIR_model_loader_v2"
                     "SUPIR_model_loader_v2_clip", "SUPIR_Upscale"
------------------------------------
ComfyUI-Universal-Styler "ShowText|pysssss", "Load Nai Styles Complex CSV", "Universal_Styler_Node"
                     "concat"
------------------------------------
ComfyUI-Video-Matting "Robust Video Matting", "BRIAAI Matting"
------------------------------------
ComfyUI-VideoHelperSuite "VHS_VideoCombine", "VHS_LoadVideo", "VHS_LoadVideoPath"
                     "VHS_LoadImages", "VHS_LoadImagesPath", "VHS_LoadAudio"
                     "VHS_LoadAudioUpload", "VHS_AudioToVHSAudio", "VHS_VHSAudioToAudio"
                     "VHS_PruneOutputs", "VHS_BatchManager", "VHS_VideoInfo"
                     "VHS_VideoInfoSource", "VHS_VideoInfoLoaded", "VHS_VAEEncodeBatched"
                     "VHS_VAEDecodeBatched", "VHS_SplitLatents", "VHS_SplitImages"
                     "VHS_SplitMasks", "VHS_MergeLatents", "VHS_MergeImages"
                     "VHS_MergeMasks", "VHS_GetLatentCount", "VHS_GetImageCount"
                     "VHS_GetMaskCount", "VHS_DuplicateLatents", "VHS_DuplicateImages"
                     "VHS_DuplicateMasks", "VHS_SelectEveryNthLatent", "VHS_SelectEveryNthImage"
                     "VHS_SelectEveryNthMask", "VHS_SelectLatents", "VHS_SelectImages"
                     "VHS_SelectMasks"
------------------------------------
ComfyUI-WD14-Tagger  "WD14Tagger|pysssss"
------------------------------------
ComfyUI-eesahesNodes "InstantX Flux Union ControlNet Loader"
------------------------------------
ComfyUI-segment-anything-2 "DownloadAndLoadSAM2Model", "Sam2Segmentation", "Florence2toCoordinates"
                     "Sam2AutoSegmentation", "Sam2VideoSegmentationAddPoints", "Sam2VideoSegmentation"
------------------------------------
ComfyUI_CatVTON_Wrapper "CatVTONWrapper"
------------------------------------
ComfyUI_ChatGLM_API  "ZhipuaiApi_Txt", "ZhipuaiApi_img", "ZhipuaiApi_Character"
                     "Glm_4_9b_Chat", "Glm_4v_9b", "Glm_Lcoal_Or_Repo"
------------------------------------
ComfyUI_Comfyroll_CustomNodes "CR Image Output", "CR Latent Batch Size", "CR Conditioning Mixer"
                     "CR Select Model", "CR Seed", "CR Prompt Text"
                     "CR Combine Prompt", "CR VAE Decode", "CR Text List"
                     "CR Prompt List", "CR Simple List", "CR Float Range List"
                     "CR Integer Range List", "CR Load Text List", "CR Binary To Bit List"
                     "CR Text Cycler", "CR Value Cycler", "CR Load Image List"
                     "CR Load Image List Plus", "CR Load GIF As List", "CR Font File List"
                     "CR Batch Images From List", "CR Intertwine Lists", "CR Repeater"
                     "CR XY Product", "CR Text List To String", "CR SD1.5 Aspect Ratio"
                     "CR SDXL Aspect Ratio", "CR Aspect Ratio", "CR Aspect Ratio Banners"
                     "CR Aspect Ratio Social Media", "CR_Aspect Ratio For Print", "CR Image Size"
                     "CR Aspect Ratio SDXL", "CR SDXL Prompt Mixer", "CR Seed to Int"
                     "CR Apply ControlNet", "CR Multi-ControlNet Stack", "CR Apply Multi-ControlNet"
                     "CR Load LoRA", "CR LoRA Stack", "CR Random LoRA Stack"
                     "CR Random Weight LoRA", "CR Apply LoRA Stack", "CR Apply Model Merge"
                     "CR Model Merge Stack", "CR Data Bus In", "CR Data Bus Out"
                     "CR 8 Channel In", "CR 8 Channel Out", "CR Module Pipe Loader"
                     "CR Module Input", "CR Module Output", "CR Image Pipe In"
                     "CR Image Pipe Edit", "CR Image Pipe Out", "CR Pipe Switch"
                     "CR SDXL Prompt Mix Presets", "CR SDXL Style Text", "CR SDXL Base Prompt Encoder"
                     "CR Multi Upscale Stack", "CR Upscale Image", "CR Apply Multi Upscale"
                     "CR XY List", "CR XY Interpolate", "CR XY From Folder"
                     "CR XY Save Grid Image", "CR XY Index", "CR Halftone Grid"
                     "CR Color Bars", "CR Style Bars", "CR Checker Pattern"
                     "CR Polygons", "CR Color Gradient", "CR Radial Gradient"
                     "CR Starburst Lines", "CR Starburst Colors", "CR Simple Binary Pattern"
                     "CR Binary Pattern", "CR Draw Shape", "CR Draw Pie"
                     "CR Random Shape Pattern", "CR Overlay Text", "CR Draw Text"
                     "CR Mask Text", "CR Composite Text", "CR Simple Text Watermark"
                     "CR Select Font", "CR Halftone Filter", "CR Color Tint"
                     "CR Vignette Filter", "CR Page Layout", "CR Image Panel"
                     "CR Image Grid Panel", "CR Image Border", "CR Feathered Border"
                     "CR Simple Text Panel", "CR Color Panel", "CR Overlay Transparent Image"
                     "CR Half Drop Panel", "CR Diamond Panel", "CR Simple Meme Template"
                     "CR Simple Banner", "CR Comic Panel Templates", "CR Simple Image Compare"
                     "CR Thumbnail Preview", "CR Seamless Checker", "CR Image Input Switch"
                     "CR Image Input Switch (4 way)", "CR Latent Input Switch", "CR Conditioning Input Switch"
                     "CR Clip Input Switch", "CR Model Input Switch", "CR ControlNet Input Switch"
                     "CR VAE Input Switch", "CR Text Input Switch", "CR Text Input Switch (4 way)"
                     "CR Switch Model and CLIP", "CR Batch Process Switch", "CR Img2Img Process Switch"
                     "CR Hires Fix Process Switch", "CR Index", "CR Index Increment"
                     "CR Index Multiply", "CR Index Reset", "CR Trigger"
                     "CR String To Number", "CR String To Combo", "CR Float To String"
                     "CR Float To Integer", "CR Integer To String", "CR String To Boolean"
                     "CR Random Hex Color", "CR Random RGB", "CR Random Multiline Values"
                     "CR Random Multiline Colors", "CR Random RGB Gradient", "CR Random Panel Codes"
                     "CR Text", "CR Multiline Text", "CR Split String"
                     "CR Text Concatenate", "CR Text Replace", "CR Text Length"
                     "CR Text Operation", "CR Text Blacklist", "CR Save Text To File"
                     "CR Set Value On Boolean", "CR Set Value On Binary", "CR Set Value on String"
                     "CR Set Switch From String", "CR Value", "CR Integer Multiple"
                     "CR Clamp Value", "CR Math Operation", "CR Get Parameter From Prompt"
                     "CR Select Resize Method", "CR Select ISO Size", "CR Simple Schedule"
                     "CR Central Schedule", "CR Combine Schedules", "CR Output Schedule To File"
                     "CR Load Schedule From File", "CR Schedule Input Switch", "CR Bit Schedule"
                     "CR Simple Value Scheduler", "CR Simple Text Scheduler", "CR Value Scheduler"
                     "CR Text Scheduler", "CR Load Scheduled Models", "CR Load Scheduled LoRAs"
                     "CR Prompt Scheduler", "CR Simple Prompt Scheduler", "CR Keyframe List"
                     "CR Encode Scheduled Prompts", "CR Gradient Float", "CR Gradient Integer"
                     "CR Increment Float", "CR Increment Integer", "CR Interpolate Latents"
                     "CR Debatch Frames", "CR Current Frame", "CR Load Animation Frames"
                     "CR Load Flow Frames", "CR Output Flow Frames", "CR Prompt List Keyframes"
                     "CR Simple Prompt List", "CR Simple Prompt List Keyframes", "CR Cycle Models"
                     "CR Cycle LoRAs", "CR Cycle Text", "CR Cycle Text Simple"
                     "CR Cycle Images", "CR Cycle Images Simple", "CR Model List"
                     "CR LoRA List", "CR Text List Simple", "CR Image List"
                     "CR Image List Simple"
------------------------------------
ComfyUI_Custom_Nodes_AlekPet "ArgosTranslateCLIPTextEncodeNode", "ArgosTranslateTextNode", "DeepTranslatorCLIPTextEncodeNode"
                     "DeepTranslatorTextNode", "PreviewTextNode", "HexToHueNode"
                     "ColorsCorrectNode", "GoogleTranslateCLIPTextEncodeNode", "GoogleTranslateTextNode"
                     "PainterNode", "PoseNode", "IDENode"
------------------------------------
ComfyUI_Fill-Nodes   "FL_ImageRandomizer", "FL_ImageCaptionSaver", "FL_ImageDimensionDisplay"
                     "FL_CodeNode", "FL_ImagePixelator", "FL_DirectoryCrawl"
                     "FL_Ascii", "FL_Glitch", "FL_Ripple"
                     "FL_PixelSort", "FL_HexagonalPattern", "FL_NFTGenerator"
                     "FL_HalftonePattern", "FL_RandomNumber", "FL_PromptSelector"
                     "FL_Shadertoy", "FL_PixelArtShader", "FL_InfiniteZoom"
                     "FL_PaperDrawn", "FL_ImageNotes", "FL_ImageCollage"
                     "FL_KsamplerSettings", "FL_RetroEffect", "FL_InpaintCrop"
                     "FL_Inpaint_Stitch", "FL_SDUltimate_Slices", "FL_BatchAlign"
                     "FL_VideoRecompose", "FL_VideoCropMask", "FL_SeparateMaskComponents"
                     "FL_PasteOnCanvas", "FL_BulletHellGame", "FL_TetrisGame"
                     "FL_Dither", "FL_SystemCheck", "FL_ColorPicker"
                     "FL_GradGenerator", "FL_MirrorAndAppendCaptions", "FL_ImageCaptionLayout"
                     "FL_HFHubModelUploader", "FL_ZipDirectory", "FL_ZipSave"
                     "FL_GPT_Vision", "FL_TimeLine", "FL_SimpleGPTVision"
                     "FL_SendToDiscordWebhook", "FL_HF_Character", "FL_CaptionToCSV"
                     "FL_KsamplerPlus", "FL_KsamplerBasic", "FL_FractalKSampler"
                     "FL_UpscaleModel", "FL_SaveCSV", "FL_KSamplerXYZPlot"
                     "FL_SamplerStrings", "FL_SchedulerStrings", "FL_ImageCaptionLayoutPDF"
                     "FL_Dalle3", "FL_SaveImages", "FL_LoadImage"
                     "FL_PDFLoader", "FL_PDFToImages", "FL_PDFSaver"
                     "FL_ImagesToPDF", "FL_PDFMerger", "FL_PDFTextExtractor"
                     "FL_PDFImageExtractor", "FL_BulkPDFLoader", "FL_SaveAndDisplayImage"
                     "FL_OllamaCaptioner", "FL_ImageAdjuster", "GradientImageGenerator"
------------------------------------
ComfyUI_IPAdapter_plus "IPAdapter", "IPAdapterAdvanced", "IPAdapterBatch"
                     "IPAdapterFaceID", "IPAdapterFaceIDKolors", "IPAAdapterFaceIDBatch"
                     "IPAdapterTiled", "IPAdapterTiledBatch", "IPAdapterEmbeds"
                     "IPAdapterEmbedsBatch", "IPAdapterStyleComposition", "IPAdapterStyleCompositionBatch"
                     "IPAdapterMS", "IPAdapterClipVisionEnhancer", "IPAdapterClipVisionEnhancerBatch"
                     "IPAdapterFromParams", "IPAdapterPreciseStyleTransfer", "IPAdapterPreciseStyleTransferBatch"
                     "IPAdapterPreciseComposition", "IPAdapterPreciseCompositionBatch", "IPAdapterUnifiedLoader"
                     "IPAdapterUnifiedLoaderFaceID", "IPAdapterModelLoader", "IPAdapterInsightFaceLoader"
                     "IPAdapterUnifiedLoaderCommunity", "IPAdapterEncoder", "IPAdapterCombineEmbeds"
                     "IPAdapterNoise", "PrepImageForClipVision", "IPAdapterSaveEmbeds"
                     "IPAdapterLoadEmbeds", "IPAdapterWeights", "IPAdapterCombineWeights"
                     "IPAdapterWeightsFromStrategy", "IPAdapterPromptScheduleFromWeightsStrategy", "IPAdapterRegionalConditioning"
                     "IPAdapterCombineParams"
------------------------------------
ComfyUI_LayerStyle   "LayerUtility: QWenImage2Prompt", "LayerFilter: AddGrain", "LayerUtility: BatchSelector"
                     "LayerMask: BiRefNetUltra", "LayerMask: BlendIf Mask", "LayerFilter: ChannelShake"
                     "LayerUtility: CheckMask", "LayerUtility: CheckMaskV2", "LayerColor: ColorAdapter"
                     "LayerColor: HSV", "LayerColor: LAB", "LayerColor: LUT Apply"
                     "LayerColor: RGB", "LayerColor: YUV", "LayerColor: AutoAdjust"
                     "LayerColor: AutoAdjustV2", "LayerColor: AutoBrightness", "LayerColor: Brightness & Contrast"
                     "LayerColor: ColorBalance", "LayerColor: ColorTemperature", "LayerColor: Exposure"
                     "LayerColor: Gamma", "LayerColor: Levels", "LayerColor: Color of Shadow & Highlight"
                     "LayerUtility: ColorImage", "LayerUtility: ColorImage V2", "LayerFilter: ColorMap"
                     "LayerStyle: ColorOverlay V2", "LayerStyle: ColorOverlay", "LayerUtility: ColorPicker"
                     "LayerUtility: HSV Value", "LayerUtility: RGB Value", "LayerMask: CreateGradientMask"
                     "LayerUtility: CropBoxResolve", "LayerUtility: CropByMask", "LayerUtility: CropByMask V2"
                     "LayerUtility: QueueStop", "LayerUtility: SwitchCase", "LayerUtility: If "
                     "LayerUtility: StringCondition", "LayerUtility: BooleanOperator", "LayerUtility: NumberCalculator"
                     "LayerUtility: BooleanOperatorV2", "LayerUtility: NumberCalculatorV2", "LayerUtility: TextBox"
                     "LayerUtility: String", "LayerUtility: Integer", "LayerUtility: Float"
                     "LayerUtility: Boolean", "LayerUtility: Seed", "LayerStyle: DropShadow"
                     "LayerStyle: DropShadow V2", "LayerUtility: ExtendCanvas", "LayerUtility: ExtendCanvasV2"
                     "LayerFilter: Film", "LayerFilter: FilmV2", "LayerMask: Florence2Ultra"
                     "LayerMask: LoadFlorence2Model", "LayerUtility: Florence2Image2Prompt", "LayerFilter: GaussianBlur"
                     "LayerUtility: GetColorTone", "LayerUtility: GetColorToneV2", "LayerUtility: GetImageSize"
                     "LayerUtility: GradientImage", "LayerUtility: GradientImage V2", "LayerStyle: GradientOverlay"
                     "LayerStyle: GradientOverlay V2", "LayerFilter: HDREffects", "LayerUtility: HLFrequencyDetailRestore"
                     "LayerUtility: ImageAutoCrop", "LayerUtility: ImageAutoCrop V2", "LayerUtility: ImageBlend"
                     "LayerUtility: ImageBlendAdvance", "LayerUtility: ImageBlendAdvance V2", "LayerUtility: ImageBlend V2"
                     "LayerUtility: ImageChannelMerge", "LayerUtility: ImageChannelSplit", "LayerUtility: ImageCombineAlpha"
                     "LayerUtility: ImageHub", "LayerUtility: ImageMaskScaleAs", "LayerUtility: ImageOpacity"
                     "LayerUtility: ImageReel", "LayerUtility: ImageReelComposit", "LayerUtility: ImageRemoveAlpha"
                     "LayerUtility: ImageRewardFilter", "LayerUtility: ImageScaleByAspectRatio", "LayerUtility: ImageScaleByAspectRatio V2"
                     "LayerUtility: ImageScaleRestore", "LayerUtility: ImageScaleRestore V2", "LayerUtility: ImageShift"
                     "LayerMask: ImageToMask", "LayerStyle: InnerGlow", "LayerStyle: InnerGlow V2"
                     "LayerStyle: InnerShadow", "LayerStyle: InnerShadow V2", "LayerUtility: LaMa"
                     "LayerUtility: LayerImageTransform", "LayerUtility: LayerMaskTransform", "LayerFilter: LightLeak"
                     "LayerUtility: LoadPSD", "LayerMask: MaskBoxDetect", "LayerMask: MaskByColor"
                     "LayerMask: MaskByDifferent", "LayerMask: MaskEdgeShrink", "LayerMask: MaskEdgeUltraDetail"
                     "LayerMask: MaskEdgeUltraDetail V2", "LayerMask: MaskGradient", "LayerMask: MaskGrain"
                     "LayerMask: MaskGrow", "LayerMask: MaskInvert", "LayerMask: MaskMotionBlur"
                     "LayerMask: MaskPreview", "LayerMask: MaskStroke", "LayerMask: MediapipeFacialSegment"
                     "LayerFilter: MotionBlur", "LayerStyle: OuterGlow", "LayerStyle: OuterGlow V2"
                     "LayerMask: PersonMaskUltra", "LayerMask: PersonMaskUltra V2", "LayerMask: PixelSpread"
                     "LayerUtility: PrintInfo", "LayerUtility: PromptEmbellish", "LayerUtility: PromptTagger"
                     "LayerUtility: PurgeVRAM", "LayerUtility: CreateQRCode", "LayerUtility: DecodeQRCode"
                     "LayerUtility: RestoreCropBox", "LayerMask: RemBgUltra", "LayerMask: RmBgUltra V2"
                     "LayerUtility: SaveImagePlus", "LayerUtility: SD3NegativeConditioning", "LayerMask: SegformerB2ClothesUltra"
                     "LayerMask: SegformerUltraV2", "LayerMask: SegformerClothesPipelineLoader", "LayerMask: SegformerFashionPipelineLoader"
                     "LayerMask: SegmentAnythingUltra", "LayerMask: SegmentAnythingUltra V2", "LayerMask: Shadow & Highlight Mask"
                     "LayerFilter: Sharp & Soft", "LayerUtility: SimpleTextImage", "LayerFilter: SkinBeauty"
                     "LayerFilter: SoftLight", "LayerStyle: Stroke", "LayerStyle: Stroke V2"
                     "LayerUtility: TextImage", "LayerUtility: TextJoin", "LayerMask: TransparentBackgroundUltra"
                     "LayerFilter: WaterColor", "LayerUtility: AddBlindWaterMark", "LayerUtility: ShowBlindWaterMark"
                     "LayerUtility: XY to Percent", "LayerMask: YoloV8Detect", "LayerColor: RGB"
------------------------------------
ComfyUI_ProPainter_Nodes "ProPainterInpaint", "ProPainterOutpaint"
------------------------------------
ComfyUI_UltimateSDUpscale "UltimateSDUpscale", "UltimateSDUpscaleNoUpscale", "UltimateSDUpscaleCustomSample"
------------------------------------
ComfyUI_tinyterraNodes "ttN compareInput", "ttN tinyLoader", "ttN conditioning"
                     "ttN KSampler_v2", "ttN pipeLoader_v2", "ttN pipeKSampler_v2"
                     "ttN pipeKSamplerAdvanced_v2", "ttN pipeLoaderSDXL_v2", "ttN pipeKSamplerSDXL_v2"
                     "ttN advanced xyPlot", "ttN advPlot images", "ttN advPlot range"
                     "ttN advPlot string", "ttN advPlot combo", "ttN pipeEDIT"
                     "ttN pipe2BASIC", "ttN pipe2DETAILER", "ttN pipeEncodeConcat"
                     "ttN pipeLoraStack", "ttN multiModelMerge", "ttN debugInput"
                     "ttN text", "ttN textDebug", "ttN concat"
                     "ttN text3BOX_3WAYconcat", "ttN text7BOX_concat", "ttN textCycleLine"
                     "ttN textOutput", "ttN imageOutput", "ttN imageREMBG"
                     "ttN hiresfixScale", "ttN int", "ttN float"
                     "ttN seed", "ttN xyPlot", "ttN pipeIN"
                     "ttN pipeOUT", "ttN pipeLoader", "ttN pipeKSampler"
                     "ttN pipeKSamplerAdvanced", "ttN pipeLoaderSDXL", "ttN pipeKSamplerSDXL"
------------------------------------
Comfyui-Yolov8       "Yolov8Detection", "Yolov8Segmentation"
------------------------------------
Comfyui-Yolov8-JSON  "Load Yolov8 Model", "Load Yolov8 Model From Path", "Apply Yolov8 Model"
                     "Apply Yolov8 Model Seg", "Save Labelme Json", "Draw Labelme Json"
------------------------------------
DebugNode-ComfyUI    "WTFDebugNode"
------------------------------------
VEnhancer            "VEnhancerNode", "VEnhancerNode", "ConditioningCombine"
                     "VEnhancerNode", "VEnhancerNode", "ConditioningCombine"
------------------------------------
anynode              "AnyNode", "AnyNodeGemini", "AnyNodeLocal"
                     "AnyNodeAnthropic"
------------------------------------
canvas_tab           "Canvas_Tab", "Send_To_Editor"
------------------------------------
cg-use-everywhere    "Seed Everywhere"
------------------------------------
comfyui-art-venture  "AV_CheckpointModelsToParametersPipe", "AV_PromptsToParametersPipe", "AV_ParametersPipeToCheckpointModels"
                     "AV_ParametersPipeToPrompts", "AV_VAELoader", "AV_LoraLoader"
                     "AV_LoraListLoader", "AV_LoraListStacker", "AV_CheckpointMerge"
                     "AV_CheckpointSave", "LoadImageFromUrl", "LoadImageAsMaskFromUrl"
                     "StringToInt", "StringToNumber", "BooleanPrimitive"
                     "ImageMuxer", "ImageScaleDown", "ImageScaleDownBy"
                     "ImageScaleDownToSize", "ImageScaleToMegapixels", "ImageAlphaComposite"
                     "ImageGaussianBlur", "ImageRepeat", "ImageExtractChannel"
                     "ImageApplyChannel", "QRCodeGenerator", "DependenciesEdit"
                     "AspectRatioSelector", "SDXLAspectRatioSelector", "SeedSelector"
                     "CheckpointNameSelector", "LoadJsonFromUrl", "GetObjectFromJson"
                     "GetTextFromJson", "GetFloatFromJson", "GetIntFromJson"
                     "GetBoolFromJson", "RandomInt", "RandomFloat"
                     "NumberScaler", "MergeModels", "AV_ControlNetLoader"
                     "AV_ControlNetEfficientLoader", "AV_ControlNetEfficientLoaderAdvanced", "AV_ControlNetEfficientStacker"
                     "AV_ControlNetEfficientStackerSimple", "AV_ControlNetPreprocessor", "Fooocus_KSampler"
                     "Fooocus_KSamplerAdvanced", "AV_SAMLoader", "GetSAMEmbedding"
                     "SAMEmbeddingToImage", "LaMaInpaint", "PrepareImageAndMaskForInpaint"
                     "OverlayInpaintedLatent", "OverlayInpaintedImage", "BLIPLoader"
                     "BLIPCaption", "DeepDanbooruCaption", "ISNetLoader"
                     "ISNetSegment", "AV_OpenAIApi", "AV_ClaudeApi"
                     "AV_AwsBedrockClaudeApi", "AV_AwsBedrockMistralApi", "AV_LLMApiConfig"
                     "AV_LLMMessage", "AV_LLMChat", "AV_LLMCompletion"
                     "ColorBlend", "ColorCorrect", "SDXLPromptStyler"
------------------------------------
comfyui-browser      "LoadImageByUrl //Browser", "SelectInputs //Browser", "XyzPlot //Browser"
                     "DifyTextGenerator //Browser", "UploadToRemote //Browser"
------------------------------------
comfyui-inpaint-nodes "INPAINT_LoadFooocusInpaint", "INPAINT_ApplyFooocusInpaint", "INPAINT_VAEEncodeInpaintConditioning"
                     "INPAINT_MaskedFill", "INPAINT_MaskedBlur", "INPAINT_LoadInpaintModel"
                     "INPAINT_InpaintWithModel", "INPAINT_ExpandMask", "INPAINT_DenoiseToCompositingMask"
------------------------------------
comfyui-lama-remover "LamaRemover", "LamaRemoverIMG"
------------------------------------
comfyui-mask-boundingbox "Mask Bounding Box"
------------------------------------
comfyui-put-image    "PutImage"
------------------------------------
comfyui-tensorops    "ChannelSelector", "MaskImage", "SaveImageToS3"
                     "SaveJsonToSurreal", "SaveTextToSurreal", "FetchJsonFromSurreal"
                     "ForegroundMask", "SaveToRedis", "FetchFromRedis"
                     "FalDifferentialDiffusion", "FalDiffusion", "BackgroundSelect"
                     "GetLayerMask", "SendImageOnWebSocket", "SendJsonOnWebSocket"
                     "DownloadAndLoadFlorence2Model", "Florence2Run", "DownloadAndLoadSAM2Model"
                     "Sam2Segmentation", "Florence2toCoordinates", "Sam2AutoSegmentation"
                     "Sam2VideoSegmentationAddPoints", "Sam2VideoSegmentation"
------------------------------------
comfyui-tooling-nodes "ETN_LoadImageBase64", "ETN_LoadMaskBase64", "ETN_SendImageWebSocket"
                     "ETN_CropImage", "ETN_ApplyMaskToImage", "ETN_TileLayout"
                     "ETN_ExtractImageTile", "ETN_ExtractMaskTile", "ETN_GenerateTileMask"
                     "ETN_MergeImageTile", "ETN_BackgroundRegion", "ETN_DefineRegion"
                     "ETN_ListRegionMasks", "ETN_AttentionMask", "ETN_NSFWFilter"
                     "ETN_Translate"
------------------------------------
comfyui_controlnet_aux "AIO_Preprocessor", "ControlNetPreprocessorSelector", "ExecuteAllControlNetPreprocessors"
                     "ControlNetAuxSimpleAddText", "PixelPerfectResolution", "ImageGenResolutionFromImage"
                     "ImageGenResolutionFromLatent", "HintImageEnchance", "AnimeFace_SemSegPreprocessor"
                     "AnyLineArtPreprocessor_aux", "BinaryPreprocessor", "CannyEdgePreprocessor"
                     "ColorPreprocessor", "DensePosePreprocessor", "DepthAnythingPreprocessor"
                     "Zoe_DepthAnythingPreprocessor", "DepthAnythingV2Preprocessor", "DiffusionEdge_Preprocessor"
                     "DSINE-NormalMapPreprocessor", "DWPreprocessor", "AnimalPosePreprocessor"
                     "HEDPreprocessor", "FakeScribblePreprocessor", "InpaintPreprocessor"
                     "LeReS-DepthMapPreprocessor", "LineArtPreprocessor", "AnimeLineArtPreprocessor"
                     "LineartStandardPreprocessor", "Manga2Anime_LineArt_Preprocessor", "MediaPipe-FaceMeshPreprocessor"
                     "MeshGraphormer-DepthMapPreprocessor", "MeshGraphormer+ImpactDetector-DepthMapPreprocessor", "Metric3D-DepthMapPreprocessor"
                     "Metric3D-NormalMapPreprocessor", "MiDaS-NormalMapPreprocessor", "MiDaS-DepthMapPreprocessor"
                     "M-LSDPreprocessor", "BAE-NormalMapPreprocessor", "OneFormer-COCO-SemSegPreprocessor"
                     "OneFormer-ADE20K-SemSegPreprocessor", "OpenposePreprocessor", "PiDiNetPreprocessor"
                     "SavePoseKpsAsJsonFile", "FacialPartColoringFromPoseKps", "UpperBodyTrackingFromPoseKps"
                     "RenderPeopleKps", "RenderAnimalKps", "ImageLuminanceDetector"
                     "ImageIntensityDetector", "ScribblePreprocessor", "Scribble_XDoG_Preprocessor"
                     "Scribble_PiDiNet_Preprocessor", "SAMPreprocessor", "ShufflePreprocessor"
                     "TEEDPreprocessor", "TilePreprocessor", "TTPlanet_TileGF_Preprocessor"
                     "TTPlanet_TileSimple_Preprocessor", "UniFormer-SemSegPreprocessor", "SemSegPreprocessor"
                     "Unimatch_OptFlowPreprocessor", "MaskOptFlow", "Zoe-DepthMapPreprocessor"
------------------------------------
comfyui_segment_anything "SAMModelLoader (segment anything)", "GroundingDinoModelLoader (segment anything)", "GroundingDinoSAMSegment (segment anything)"
                     "InvertMask (segment anything)", "IsMaskEmpty"
------------------------------------
masquerade-nodes-comfyui "Mask By Text", "Mask Morphology", "Combine Masks"
                     "Unary Mask Op", "Unary Image Op", "Blur"
                     "Image To Mask", "Mix Images By Mask", "Mix Color By Mask"
                     "Mask To Region", "Cut By Mask", "Paste By Mask"
                     "Get Image Size", "Change Channel Count", "Constant Mask"
                     "Prune By Mask", "Separate Mask Components", "Create Rect Mask"
                     "Make Image Batch", "Create QR Code", "Convert Color Space"
                     "MasqueradeIncrementer"
------------------------------------
qsOpenSoraPlan_v2    "QS_OpenSoraI2V", "QS_OpenSora_ModelLoader", "QS_OpenSoraI2V"
                     "QS_OpenSora_ModelLoader", "QS_OpenSoraI2V", "QS_OpenSora_ModelLoader"
                     "QS_OpenSoraI2V", "QS_OpenSora_ModelLoader"
------------------------------------
qsRevideo            "Q1sRevideo", "Q1sRevideoInner", "Q1sRevideoLoop"
                     "Q1sRevideoSampleLoop", "Q2sRevideo", "Q2sRevideoInner"
                     "Q2sRevideoLoop", "Q2sRevideoSampleLoop", "QsRevideo"
                     "QsRevideoInner", "QsRevideoLoop", "QsRevideoSampleLoop"
                     "Q1sRevideo", "Q1sRevideoInner", "Q1sRevideoLoop"
                     "Q1sRevideoSampleLoop", "Q2sRevideo", "Q2sRevideoInner"
                     "Q2sRevideoLoop", "Q2sRevideoSampleLoop", "QsRevideo"
                     "QsRevideoInner", "QsRevideoLoop", "QsRevideoSampleLoop"
------------------------------------
qs_comfy_nodes       "QSLoadDPX", "QSSaveDPX", "QSLoadEXR"
                     "QSSaveEXR", "MotionLatent", "TensorShape"
                     "CombineLatent", "InputIntrospection", "QsKSamplerAdvanced"
                     "Latent_Reference_Concat", "QSImageTilesCombine", "MotionLatent"
                     "TensorShape", "CombineLatent", "InputIntrospection"
                     "QsKSamplerAdvanced", "Latent_Reference_Concat", "QSImageTilesCombine"
------------------------------------
qscomfy_dynamicrafter_native "QSDynamiCrafterLoader", "QSDynamiCrafterProcessor"
------------------------------------
st2v                 "StreamingT2V"
------------------------------------
was-node-suite-comfyui "BLIP Model Loader", "Blend Latents", "Bus Node"
                     "Cache Node", "Checkpoint Loader", "Checkpoint Loader (Simple)"
                     "CLIPTextEncode (NSP)", "CLIP Input Switch", "CLIP Vision Input Switch"
                     "Conditioning Input Switch", "Constant Number", "Create Grid Image"
                     "Create Grid Image from Batch", "Create Morph Image", "Create Morph Image from Path"
                     "Create Video from Path", "CLIPSeg Masking", "CLIPSeg Model Loader"
                     "CLIPSeg Batch Masking", "Convert Masks to Images", "Control Net Model Input Switch"
                     "Debug Number to Console", "Dictionary to Console", "Diffusers Model Loader"
                     "Diffusers Hub Model Down-Loader", "Export API", "Latent Input Switch"
                     "Load Cache", "Logic Boolean", "Logic Boolean Primitive"
                     "Logic Comparison OR", "Logic Comparison AND", "Logic Comparison XOR"
                     "Logic NOT", "Lora Loader", "Image SSAO (Ambient Occlusion)"
                     "Image SSDO (Direct Occlusion)", "Image Analyze", "Image Aspect Ratio"
                     "Image Batch", "Image Blank", "Image Blend by Mask"
                     "Image Blend", "Image Blending Mode", "Image Bloom Filter"
                     "Image Canny Filter", "Image Chromatic Aberration", "Image Color Palette"
                     "Image Crop Face", "Image Crop Location", "Image Crop Square Location"
                     "Image Displacement Warp", "Image Lucy Sharpen", "Image Paste Face"
                     "Image Paste Crop", "Image Paste Crop by Location", "Image Pixelate"
                     "Image Power Noise", "Image Dragan Photography Filter", "Image Edge Detection Filter"
                     "Image Film Grain", "Image Filter Adjustments", "Image Flip"
                     "Image Gradient Map", "Image Generate Gradient", "Image High Pass Filter"
                     "Image History Loader", "Image Input Switch", "Image Levels Adjustment"
                     "Image Load", "Image Median Filter", "Image Mix RGB Channels"
                     "Image Monitor Effects Filter", "Image Nova Filter", "Image Padding"
                     "Image Perlin Noise", "Image Rembg (Remove Background)", "Image Perlin Power Fractal"
                     "Image Remove Background (Alpha)", "Image Remove Color", "Image Resize"
                     "Image Rotate", "Image Rotate Hue", "Image Save"
                     "Image Seamless Texture", "Image Select Channel", "Image Select Color"
                     "Image Shadows and Highlights", "Image Size to Number", "Image Stitch"
                     "Image Style Filter", "Image Threshold", "Image Tiled"
                     "Image Transpose", "Image fDOF Filter", "Image to Latent Mask"
                     "Image to Noise", "Image to Seed", "Images to RGB"
                     "Images to Linear", "Integer place counter", "Image Voronoi Noise Filter"
                     "KSampler (WAS)", "KSampler Cycle", "Latent Batch"
                     "Latent Noise Injection", "Latent Size to Number", "Latent Upscale by Factor (WAS)"
                     "Load Image Batch", "Load Text File", "Load Lora"
                     "Lora Input Switch", "Masks Add", "Masks Subtract"
                     "Mask Arbitrary Region", "Mask Batch to Mask", "Mask Batch"
                     "Mask Ceiling Region", "Mask Crop Dominant Region", "Mask Crop Minority Region"
                     "Mask Crop Region", "Mask Paste Region", "Mask Dilate Region"
                     "Mask Dominant Region", "Mask Erode Region", "Mask Fill Holes"
                     "Mask Floor Region", "Mask Gaussian Region", "Mask Invert"
                     "Mask Minority Region", "Mask Smooth Region", "Mask Threshold Region"
                     "Masks Combine Regions", "Masks Combine Batch", "MiDaS Model Loader"
                     "MiDaS Depth Approximation", "MiDaS Mask Image", "Model Input Switch"
                     "Number Counter", "Number Operation", "Number to Float"
                     "Number Input Switch", "Number Input Condition", "Number Multiple Of"
                     "Number PI", "Number to Int", "Number to Seed"
                     "Number to String", "Number to Text", "Boolean To Text"
                     "Prompt Styles Selector", "Prompt Multiple Styles Selector", "Random Number"
                     "Save Text File", "Seed", "Tensor Batch to Image"
                     "BLIP Analyze Image", "SAM Model Loader", "SAM Parameters"
                     "SAM Parameters Combine", "SAM Image Mask", "Samples Passthrough (Stat System)"
                     "String to Text", "Image Bounds", "Inset Image Bounds"
                     "Bounded Image Blend", "Bounded Image Blend with Mask", "Bounded Image Crop"
                     "Bounded Image Crop with Mask", "Image Bounds to Console", "Text Dictionary Update"
                     "Text Dictionary Get", "Text Dictionary Convert", "Text Dictionary New"
                     "Text Dictionary Keys", "Text Dictionary To Text", "Text Add Tokens"
                     "Text Add Token by Input", "Text Compare", "Text Concatenate"
                     "Text File History Loader", "Text Find and Replace by Dictionary", "Text Find and Replace Input"
                     "Text Find and Replace", "Text Input Switch", "Text List"
                     "Text List Concatenate", "Text List to Text", "Text Load Line From File"
                     "Text Multiline", "Text Parse A1111 Embeddings", "Text Parse Noodle Soup Prompts"
                     "Text Parse Tokens", "Text Random Line", "Text Random Prompt"
                     "Text String", "Text Contains", "Text Shuffle"
                     "Text to Conditioning", "Text to Console", "Text to Number"
                     "Text to String", "Text String Truncate", "True Random.org Number Generator"
                     "unCLIP Checkpoint Loader", "Upscale Model Loader", "Upscale Model Switch"
                     "Write to GIF", "Write to Video", "VAE Input Switch"
                     "Video Dump Frames"
------------------------------------
