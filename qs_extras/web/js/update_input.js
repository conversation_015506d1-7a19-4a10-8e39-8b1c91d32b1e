// import { app } from "../../../scripts/app.js";
const app = window.comfyAPI.app.app;

console.log("qs_utils.js loaded");

// Get the ANY_TYPE from the backend
let any;

app.registerExtension({
    name: "qs_utils",
    async setup() {
        // Fetch the ANY_TYPE value from the API
        const response = await fetch('/qs_utils/api/ANY_TYPE');
        any = await response.text();
        console.log("Loaded ANY_TYPE:", any);
    },

    async beforeRegisterNodeDef(nodeType, nodeData, app) {
        if (!nodeData?.category?.startsWith("QS")) {
            return;
        }
        switch (nodeData.name) {
            case "QSPack":
                nodeType.prototype.onNodeCreated = function () {
                    this._type1 = any;
                    this.inputs_offset = nodeData.name.includes("selective") ? 1 : 0
                    this.addWidget("button", "Update inputs", null, () => {
                        if (!this.inputs) {
                            this.inputs = [];
                        }
                        const target_number_of_inputs = this.widgets.find(w => w.name === "inputcount")["value"];
                        if (target_number_of_inputs === this.inputs.length) return; // already set, do nothing

                        if (target_number_of_inputs < this.inputs.length) {
                            for (let i = this.inputs.length; i >= this.inputs_offset + target_number_of_inputs; i--)
                                this.removeInput(i)
                        }
                        else {
                            for (let i = this.inputs.length + 1 - this.inputs_offset; i <= target_number_of_inputs; ++i)
                                this.addInput(`input_${i}`, this._type1);
                        }
                    });
                }
                break;
            case "QSUnpack":
                nodeType.prototype.onNodeCreated = function () {
                    this._type1 = any;
                    this.outputs_offset = nodeData.name.includes("selective") ? 1 : 0;
                    this.addWidget("button", "Update outputs", null, () => {
                        if (!this.outputs) {
                            this.outputs = [];
                        }
                        const target_number_of_outputs = this.widgets.find(w => w.name === "outputcount")["value"];
                        if (target_number_of_outputs === this.outputs.length) return; // already set, do nothing

                        if (target_number_of_outputs < this.outputs.length) {
                            for (let i = this.outputs.length; i >= this.outputs_offset + target_number_of_outputs; i--)
                                this.removeOutput(i)
                        }
                        else {
                            for (let i = this.outputs.length + 1 - this.outputs_offset; i <= target_number_of_outputs; ++i){
                                this.addOutput(`input_${i}`, this._type1);
                            }
                        }
                    });
                }
                
                const update = nodeType.prototype.onAfterExecuteNode;
				nodeType.prototype.onExecuted = function(message) {
					const r = update? update.apply(this,arguments): undefined
					let values = message["text"]
                    for (let i = 0; i <= values.length; ++i)
                        this.outputs[i]["label"]=values[i]
					return r
				}
                break;
        }
    },
});
