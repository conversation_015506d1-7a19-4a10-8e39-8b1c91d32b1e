import os

from numpy import byte
import folder_paths
import json
import urllib.request
import urllib.parse
import uuid
import random
import base64
import pickle
import server
import websocket
import json
from comfy.utils import ProgressBar


client_id = str(uuid.uuid4())

def queue_prompt(server_address,prompt, input_objects):
    #before sending the prompt, we need to send the input objects
    #first send all the object_ids 
    missing_ids = [ f'input_{id}' for id  in range(10) if f'input_{id}' not in input_objects or input_objects[f'input_{id}'] is None]
    object_ids = { 'object_ids':list(input_objects.keys()), 'missing_ids': missing_ids}
    url = f"http://{server_address}/history/object_ids/input"
    req =  urllib.request.Request(url, data=json.dumps(object_ids).encode('utf-8'))
    result = json.loads(urllib.request.urlopen(req).read())
    # result contains temp_object_store_id 
    object_store_id = result.get('object_store_id', None)
  
    # now send the objects 1 by 1
    for object_id, object_data in input_objects.items():
        bytecode_data = pickle.dumps(object_data)
        url = f"http://{server_address}/history/objects/input/{object_store_id}/{object_id}/"
        req = urllib.request.Request(
                url, 
                data=bytecode_data, 
                headers={"Content-Type": "application/octet-stream"}, 
                method="POST"
            )
        result = urllib.request.urlopen(req)
    #add object_store_id along with prompt and client_id      
    p = {"prompt": prompt, "client_id": client_id, "object_store_id": object_store_id}
    data = json.dumps(p).encode('utf-8')
    
    req =  urllib.request.Request("http://{}/prompt".format(server_address), data=data)
    return json.loads(urllib.request.urlopen(req).read())

def get_image(server_address,filename, subfolder, folder_type):
    data = {"filename": filename, "subfolder": subfolder, "type": folder_type}
    url_values = urllib.parse.urlencode(data)
    with urllib.request.urlopen("http://{}/view?{}".format(server_address, url_values)) as response:
        return response.read()

def get_history(server_address, prompt_id):
    with urllib.request.urlopen("http://{}/history/{}".format(server_address, prompt_id)) as response:
        return json.loads(response.read())

def get_objects(server_address, input_output, prompt_id):
    object_names = []
    object_data = {}
    
    print(f'prompt_id = {prompt_id} calling http://{server_address}/history/object_ids/{input_output}/{prompt_id}')
    with urllib.request.urlopen("http://{}/history/object_ids/{}/{}".format(server_address,input_output, prompt_id)) as response:
        object_names =  json.loads(response.read())
    print(f'got object_names = {object_names}')
    for object_id in object_names:
        url = f"http://{server_address}/history/objects/{input_output}/{prompt_id}/{object_id}"
        print(f'getting object for {object_id}')
        with urllib.request.urlopen(url) as response:
            bytecode_data = response.read()
            print(f'got object data {len(bytecode_data)}')
            object_data[object_id] = pickle.loads(bytecode_data)
    return object_names, object_data

def get_outputs(server_address,ws, prompt, input_objects):
    prompt_id = queue_prompt(server_address,prompt, input_objects)['prompt_id']
    #keep looking for status of execution
    #with self.progress_bar(total=len(timesteps)) as progress_bar
    #{'type': 'progress', 'data': {'value': 19, 'max': 20, 'prompt_id': 'bb9625e6-48a1-4f67-8e1f-9795fac0f03d', 'node': '3'}}
    pbars = {} # there will be many progressbars we will node as key and value as progressbar
    while True:
        out = ws.recv()
        if isinstance(out, str):
            message = json.loads(out)
            print(f'got out str-message len: {len(message)}, type:{message['type']} \n  message: {message}')
            #handle progress
            if message['type'] == 'progress':
                pmesg = message['data']
                pbar = pbars.get(pmesg['node'], None)
                if pbar is None:
                    comfy_pbar = ProgressBar(pmesg['max'])
                    pbar = comfy_pbar 
                    pbars[pmesg['node']] = pbar                  
                pbar.update(pmesg['value'])
            if message['type'] == 'executing':
                data = message['data']
                print(f'got out data {type(data)} ')
                if data['node'] is None and data['prompt_id'] == prompt_id:
                    break #Execution is done
        else:
            print(f'got out data of non str type {type(out)}')
            print('out' )
            continue #previews are binary data
    object_names, objects = get_objects(server_address,'output', prompt_id)
#    history = get_history(server_address,prompt_id)[prompt_id]
    history = {}
    #delete all progress bars
    for node_id, pbar in pbars.items():
        try:
            pbar.close()
        except :
            try:
                del pbar
            except:
                pass
    return object_names, objects, history
     
class AnyType(str):
    """A special class that is always equal in not equal comparisons. Credit to pythongosssss"""

    def __eq__(self, _) -> bool:
        return True

    def __ne__(self, __value: object) -> bool:
        return False

any = AnyType("*")

# websocket.enableTrace(True)
class QSLetExecute:
    def __init__(self):
        pass

    
    @classmethod
    def INPUT_TYPES(cls):
        return {"required": {
            "api_file": ("STRING",),
            "server_address": ("STRING",),
            },
                
            "optional": {
                "input_0": (any, {"default": None}),"input_1": (any, {"default": None}),"input_2": (any, {"default": None}),
                "input_3": (any, {"default": None}),"input_4": (any, {"default": None}),"input_5": (any, {"default": None}),
                "input_6": (any, {"default": None}),"input_7": (any, {"default": None}),"input_8": (any, {"default": None}),
                "input_9": (any, {"default": None}),
              }
        }

    RETURN_TYPES = (
           AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), 
            AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"),"STRING", 
        )
    RETURN_NAMES = ('output_0','output_1', 'output_2', 'output_3', 'output_4', 'output_5', 
                    'output_6', 'output_7', 'output_8', 'output_9', 'output_names',)
    FUNCTION = "execute_qslet"
    CATEGORY = "QS/qslets"
    def execute_qslet(self, api_file,  server_address, 
                      input_0=None, input_1=None, input_2=None, input_3=None, input_4=None, input_5=None,
                      input_6=None, input_7=None, input_8=None, input_9=None):
        
        api_file = os.path.join(folder_paths.base_path, 'qslets', api_file)
        #modify node with class_type QSLetInputs modify ints input update_int to a random number
        with open(api_file, 'r') as f:
            api = json.load(f)
        input_nodes = [q for q,v in api.items() if v.get('class_type', None) == 'QSLetInputs']
        for node_id in input_nodes:
            api[node_id]['inputs']['update_int'] = random.random()
            print(f'updated node {node_id} with random number')
        #
        all_output_names = []
        ws = None
        # websocket.enableTrace(True)
        try: 
            ws = websocket.WebSocket() 
            ws.connect("ws://{}/ws?clientId={}".format(server_address, client_id))
            input_objects = {}
            for i in range(10):
                input_objects[f'input_{i}'] = (locals()).get(f'input_{i}', None)
            
            print(f'input_objects = {input_objects}')
            
            object_names, objects, history = get_outputs(server_address, ws, api, input_objects)
            
            outputs = [None] * 10  # Predefine a list
            [output_0,output_1, output_2, output_3, output_4, output_5, output_6, output_7, output_8, output_9] = outputs
            try : 
                for id, object_name in enumerate(object_names):
                    all_output_names.append(object_name)
                    outputs[id] = objects.get(object_name, None)
                output_0,output_1, output_2, output_3, output_4, output_5, output_6, output_7, output_8, output_9 = outputs
                     
            except Exception as e:
                print(e)
                ws.close()
                return None, None, None, None, None, None, None, None, None, None, []
            finally:
                ws.close()
            print('all_output_names', all_output_names)
            return output_0, output_1, output_2, output_3, output_4, output_5, output_6, output_7, output_8, output_9, all_output_names
    
        except Exception as e:
            if ws is not None:
                ws.close()
            print(e)
            return None, None, None, None, None, None, None, None, None, None,[]
        
#This class needs to be used by the workflow auther to set the outputs to be exposed
class QSOutput:
    def __init__(self):
        pass
    
    @classmethod
    def INPUT_TYPES(cls):
        return {"required": {
            "output_0": (any,{"default": None}),"name_0": ("STRING",{ "default": ""}),
            },
                
            "optional": {
                "output_1": (any,{"default": None}),'name_1': ("STRING",),
                "output_2": (any,{"default": None}),'name_2': ("STRING",),
                "output_3": (any,{"default": None}),'name_3': ("STRING",),
                "output_4": (any,{"default": None}),'name_4': ("STRING",),
                "output_5": (any,{"default": None}),'name_5': ("STRING",),
                "output_6": (any,{"default": None}),'name_6': ("STRING",),
                "output_7": (any,{"default": None}),'name_7': ("STRING",),
                "output_8": (any,{"default": None}),'name_8': ("STRING",),
                "output_9": (any,{"default": None}),'name_9': ("STRING",),
              }
        }
    RETURN_NAMES = ('output_names',)
    RETURN_TYPES = ('STRING',)
    FUNCTION = "send_output"
    CATEGORY = "QS/qslets"
    OUTPUT_NODE = True
    
    def send_output(self, output_0, name_0, output_1=None, name_1='', output_2=None, name_2='', 
                    output_3=None, name_3='', output_4=None, name_4='', output_5=None, name_5='',
                    output_6=None, name_6='', output_7=None, name_7='', output_8=None, name_8='',
                    output_9=None, name_9=''):
        
        server_instance = server.PromptServer.instance
        prompt_id = server_instance.last_prompt_id
        prompt_queue = server_instance.prompt_queue
        output_names = []
        
        for i in range(10):
            if locals()[f'output_{i}'] is not None and locals()[f'name_{i}'] is not None:
                data_name = locals()[f'name_{i}']
                data = locals()[f'output_{i}']
                print(f'got outputname_{i} = {data_name} output_{i} = {type(data)}   ')
                prompt_queue.add_binary_object(prompt_id, data_name , data, is_input=False)
                output_names.append(data_name)
        
        output_str = json.dumps(output_names)
        return { "ui": { "text": output_str }, "result": (output_str,) }

#This class needs to be used by the workflow author to set the inputs to be exposed all the requd inputs need to pass through this
class QSLetInputs:
    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(cls):
        return {"required": {
            "input_0": (any,),
           },
            "hidden": {"update_int": ("INT",{"default": 0})},       
            "optional": {
                "name_0": ("STRING",{ "default": ""}),
                "input_1": (any,{"default": None}), "name_1": ("STRING",{ "default": ""}),
                "input_2": (any, {"default": None}),"name_2": ("STRING",{ "default": ""}),
                "input_3": (any, {"default": None}),"name_3": ("STRING",{ "default": ""}),
                "input_4": (any, {"default": None}),"name_4": ("STRING",{ "default": ""}),
                "input_5": (any, {"default": None}),"name_5": ("STRING",{ "default": ""}),
                "input_6": (any, {"default": None}),"name_6": ("STRING",{ "default": ""}),
                "input_7": (any, {"default": None}),"name_7": ("STRING",{ "default": ""}),
                "input_8": (any, {"default": None}),"name_8": ("STRING",{ "default": ""}),
                "input_9": (any, {"default": None}),"name_9": ("STRING",{ "default": ""}),
              }
        }

    RETURN_TYPES = (
        AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), 
        AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"),AnyType("*"),"STRING", 
        )
    RETURN_NAMES = ('input_0', 'input_1', 'input_2',
                    'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_names',)
    
    FUNCTION = "qslet_inputs"
    CATEGORY = "QS/qslets"
    def qslet_inputs(self, input_0, update_int=0, name_0='', input_1=None, name_1='',
                     input_2=None, name_2='', input_3=None, name_3='',
                     input_4=None, name_4='', input_5=None, name_5='',
                     input_6=None, name_6='', input_7=None, name_7='',
                     input_8=None, name_8='', input_9=None, name_9=''):
        
        # check in the data_store if the inputs are available if assign inputs from there
        server_instance = server.PromptServer.instance
        prompt_id = server_instance.last_prompt_id
        prompt_queue = server_instance.prompt_queue
        object_ids = prompt_queue.get_object_ids(prompt_id, is_input=True)
        input_objects = {}
        for object_id in object_ids:
            input_objects[object_id] = prompt_queue.get_binary_object(prompt_id,object_id, is_input=True)
        #now go ober object_ids they will be input_0, input_1 etc if value is not None then assign it to input_0, input_1 etc
        tinput = input_objects.get('input_0', None) 
        input_0 = tinput if tinput is not None else input_0
        tinput = input_objects.get('input_1', None)
        input_1 = tinput if tinput is not None else input_1
        tinput = input_objects.get('input_2', None)
        input_2 = tinput if tinput is not None else input_2
        tinput = input_objects.get('input_3', None)
        input_3 = tinput if tinput is not None else input_3
        tinput = input_objects.get('input_4', None)
        input_4 = tinput if tinput is not None else input_4
        tinput = input_objects.get('input_5', None)
        input_5 = tinput if tinput is not None else input_5
        tinput = input_objects.get('input_6', None)
        input_6 = tinput if tinput is not None else input_6
        tinput = input_objects.get('input_7', None)
        input_7 = tinput if tinput is not None else input_7
        tinput = input_objects.get('input_8', None)
        input_8 = tinput if tinput is not None else input_8
        tinput = input_objects.get('input_9', None)
        input_9 = tinput if tinput is not None else input_9
        
        input_names = [name_0, name_1, name_2, name_3, name_4, name_5, name_6, name_7, name_8, name_9]
        input_names=json.dumps(input_names)
        return {"ui": {
            "text": input_names}, 
            "result": (input_0, input_1, input_2, input_3, input_4, input_5, input_6, input_7, input_8, input_9, input_names) 
        }
        
             
NODE_CLASS_MAPPINGS = {
    "QSLetExecute": QSLetExecute,
    "QSOutput": QSOutput,
    "QSLetInputs": QSLetInputs,
}
NODE_DISPLAY_NAME_MAPPINGS = {
    "QSLetExecute": "QS LetExecute",
    "QSOutput": "QS Output",
    "QSLetInputs": "QS LetInputs",
}

#####

# def serialize_object(obj):
#     """Serialize an object using pickle and encode it in base64."""
#     return base64.b64encode(pickle.dumps(obj)).decode('utf-8')

# def deserialize_object(data):
#     """Decode base64 and unpickle the object."""
#     return pickle.loads(base64.b64decode(data))

 # @classmethod
    # def IS_CHANGED(s, *args, **kwargs):
    #     return random.random()
   