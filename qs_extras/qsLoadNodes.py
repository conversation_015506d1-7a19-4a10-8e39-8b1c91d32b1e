import re
import folder_paths
import time
import os
import logging
import sys
import importlib
import traceback
import json
from comfy.cli_args import args
from pathlib import Path


#from qs_nodes_config import QS_NODE_CLASS_MAPPINGS, QS_NODE_DISPLAY_NAME_MAPPINGS, QS_EXTENSION_WEB_DIRS
moduleNodeClassMapping = {}
moduleDisplayNameMapping = {}
moduleWebDirMapping = {}

def qs_execute_prestartup_script():
    def execute_script(script_path):
        module_name = os.path.splitext(script_path)[0]
        try:
            spec = importlib.util.spec_from_file_location(module_name, script_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return True
        except Exception as e:
            print(f"Failed to execute startup-script: {script_path} / {e}")
        return False

    if not args.disable_all_custom_nodes:  #main taking care of this case
        return
    node_paths = folder_paths.get_folder_paths("custom_nodes")
    node_prestartup_times = []
    script_modules=['ComfyUI-Manager','ComfyUI-Marigold','qsRgt']
    custom_node_modules = [os.path.join(node_path, module) for node_path in node_paths for module in script_modules if module != ""]
    for custom_node_path in custom_node_modules:
            module_path = custom_node_path
            if os.path.isfile(module_path) or module_path.endswith(".disabled") or module_path == "__pycache__":
                continue
            script_path = os.path.join(module_path, "prestartup_script.py")
            if os.path.exists(script_path):
                time_before = time.perf_counter()
                success = execute_script(script_path)
                node_prestartup_times.append((time.perf_counter() - time_before, module_path, success))
    if len(node_prestartup_times) > 0:
        print("\nPrestartup times for custom nodes:")
        for n in sorted(node_prestartup_times):
            if n[2]:
                import_message = ""
            else:
                import_message = " (PRESTARTUP FAILED)"
            print("{:6.1f} seconds{}:".format(n[0], import_message), n[1])
        print()

def qs_preload_nodes () :
    if args.disable_all_custom_nodes:  #main taking care of this case
        preload_nodes = '["ComfyUI-Manager","ComfyUI-Marigold","qsRgt","ComfyMath","qsCTools","qsVHS","comfyui-workspace-manager"]'
        q1loader = QsLoadNodes()
        q1loader.init_external_custom_nodes(preload_nodes)
    else:
        print('No preloading of custom nodes required ')
    return

def get_module_name(module_path: str) -> str:
    """
    Returns the module name based on the given module path.
    Examples:
        get_module_name("C:/Users/<USER>/ComfyUI/custom_nodes/my_custom_node.py") -> "my_custom_node"
        get_module_name("C:/Users/<USER>/ComfyUI/custom_nodes/my_custom_node") -> "my_custom_node"
        get_module_name("C:/Users/<USER>/ComfyUI/custom_nodes/my_custom_node/") -> "my_custom_node"
        get_module_name("C:/Users/<USER>/ComfyUI/custom_nodes/my_custom_node/__init__.py") -> "my_custom_node"
        get_module_name("C:/Users/<USER>/ComfyUI/custom_nodes/my_custom_node/__init__") -> "my_custom_node"
        get_module_name("C:/Users/<USER>/ComfyUI/custom_nodes/my_custom_node/__init__/") -> "my_custom_node"
        get_module_name("C:/Users/<USER>/ComfyUI/custom_nodes/my_custom_node.disabled") -> "custom_nodes
    Args:
        module_path (str): The path of the module.
    Returns:
        str: The module name.
    """
    base_path = os.path.basename(module_path)
    if os.path.isfile(module_path):
        base_path = os.path.splitext(base_path)[0]
    return base_path

import os
import importlib.util
import sys
import logging
import traceback
moduleNodeClassMapping = {}
moduleDisplayNameMapping = {}
moduleWebDirMapping = {}

def reload_custom_node(module_path: str, ignore=set(), module_parent="custom_nodes") -> bool:
    module_name = os.path.basename(module_path)
    if os.path.isfile(module_path):
        sp = os.path.splitext(module_path)
        module_name = sp[0]
    # Invalidate importlib caches to ensure updated modules are loaded from disk
    importlib.invalidate_caches()
    # Attempt to remove the module from sys.modules if it exists
    if module_name in sys.modules:
        try:
            del sys.modules[module_name]
            moduleNodeClassMapping[module_name] = [] # empty node class list
            logging.info(f"Unloaded module {module_name}")
        except KeyError:
            logging.warning(f"Module {module_name} not found in sys.modules.")

    # Now, try to load the module again
    return load_custom_node(module_path, ignore, module_parent)

def load_custom_node(module_path: str, ignore=set(), module_parent="custom_nodes") -> bool:
    from nodes import NODE_CLASS_MAPPINGS as QS_NODE_CLASS_MAPPINGS, \
            NODE_DISPLAY_NAME_MAPPINGS as QS_NODE_DISPLAY_NAME_MAPPINGS,  \
            EXTENSION_WEB_DIRS as QS_EXTENSION_WEB_DIRS
         
    module_name = os.path.basename(module_path)
    print("Loading custom node {}".format(module_name))
    if os.path.isfile(module_path):
        sp = os.path.splitext(module_path)
        module_name = sp[0]
    try:
        logging.debug("Trying to load custom node {}".format(module_path))
        if os.path.isfile(module_path):
            module_spec = importlib.util.spec_from_file_location(module_name, module_path)
            module_dir = os.path.split(module_path)[0]
        else:
            module_spec = importlib.util.spec_from_file_location(module_name, os.path.join(module_path, "__init__.py"))
            module_dir = module_path

        module = importlib.util.module_from_spec(module_spec)
        sys.modules[module_name] = module
        module_spec.loader.exec_module(module)

        if hasattr(module, "WEB_DIRECTORY") and getattr(module, "WEB_DIRECTORY") is not None:
            web_dir = os.path.abspath(os.path.join(module_dir, getattr(module, "WEB_DIRECTORY")))
            if os.path.isdir(web_dir):
                QS_EXTENSION_WEB_DIRS[module_name] = web_dir
        moduleNodeClassMapping[module_name] = [] if moduleNodeClassMapping.get(module_name) is None else moduleNodeClassMapping[module_name]
        
        if hasattr(module, "NODE_CLASS_MAPPINGS") and getattr(module, "NODE_CLASS_MAPPINGS") is not None:
            for name, node_cls in module.NODE_CLASS_MAPPINGS.items():
                if name not in ignore:
                    moduleNodeClassMapping[module_name].append(name)
                    QS_NODE_CLASS_MAPPINGS[name] = node_cls
                    node_cls.RELATIVE_PYTHON_MODULE = "{}.{}".format(module_parent, get_module_name(module_path))
            if hasattr(module, "NODE_DISPLAY_NAME_MAPPINGS") and getattr(module, "NODE_DISPLAY_NAME_MAPPINGS") is not None:
                QS_NODE_DISPLAY_NAME_MAPPINGS.update(module.NODE_DISPLAY_NAME_MAPPINGS)
            return True
        else:
            logging.warning(f"Skip {module_path} module for custom nodes due to the lack of NODE_CLASS_MAPPINGS.")
            return False
    except Exception as e:
        logging.warning(traceback.format_exc())
        logging.warning(f"Cannot import {module_path} module for custom nodes: {e}")
        return False

class QsAddPath:
    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "custom_node_modules" : ("STRING", {"default": "[]"}),
            },
            "optional": {
                "reload_value": ("INT", {"default": 0, "min": 0, "max": 10000000, "step": 1, "round": False}),
            },
        }
    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ('PathAdded',)
    FUNCTION = "add_path"
    CATEGORY = "QS"
    OUTPUT_NODE = True


    
    def add_path(self, custom_node_modules, reload_value = 0):
     #   from nodes import QS_NODE_CLASS_MAPPINGS, QS_NODE_DISPLAY_NAME_MAPPINGS, QS_EXTENSION_WEB_DIRS
        """
        Initializes the external custom nodes.

        This function loads custom nodes from the specified folder paths and imports them into the application.
        It measures the import times for each custom node and logs the results.

        Returns:
            None
        """
        print("init_external_custom_nodes", reload_value)
        custom_node_modules = json.loads(custom_node_modules)
        loadResult = []
      #  base_node_names = set(QS_NODE_CLASS_MAPPINGS.keys())
        node_paths = folder_paths.get_folder_paths("custom_nodes")
        node_import_times = []
        custom_node_modules = [os.path.join(node_path, module) for node_path in node_paths for module in custom_node_modules if module != ""]
        for custom_node_path in custom_node_modules:
            try:
                
                if sys.path[0] != Path(custom_node_path).as_posix() and Path(custom_node_path).as_posix() not in sys.path:
                    sys.path.append(Path(custom_node_path).as_posix())
                    loadResult.append(f'True: {custom_node_path} loaded ')
                else :
                    loadResult.append(f'True: {custom_node_path} already exists in sys path')
            except Exception as e:
                print('Exception adding path ', custom_node_path ,'to sys.path', e)
                loadResult.append(f'False: Failed to load {custom_node_path} exception occured ')

        loadResult = "\n".join(loadResult)
        return (loadResult,)#{ "ui": { "node_import_times": node_import_times } }

class QsLoadNodes:
    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "custom_node_modules" : ("STRING", {"default": "[]"}),
            },
            "optional": {
                "reload_value": ("INT", {"default": 0, "min": 0, "max": 10000000, "step": 1, "round": False}),
            },
        }
    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ('LoadStatus',)
    FUNCTION = "init_external_custom_nodes"
    CATEGORY = "QS"
    OUTPUT_NODE = True


    
    def init_external_custom_nodes(self, custom_node_modules, reload_value = 0):
     #   from nodes import QS_NODE_CLASS_MAPPINGS, QS_NODE_DISPLAY_NAME_MAPPINGS, QS_EXTENSION_WEB_DIRS
        """
        Initializes the external custom nodes.

        This function loads custom nodes from the specified folder paths and imports them into the application.
        It measures the import times for each custom node and logs the results.

        Returns:
            None
        """
        print("init_external_custom_nodes", reload_value)
        custom_node_modules = json.loads(custom_node_modules)
        loadResult = []
      #  base_node_names = set(QS_NODE_CLASS_MAPPINGS.keys())
        node_paths = folder_paths.get_folder_paths("custom_nodes")
        node_import_times = []
        custom_node_modules = [os.path.join(node_path, module) for node_path in node_paths for module in custom_node_modules if module != ""]
        for custom_node_path in custom_node_modules:
                module_path = custom_node_path
                if os.path.isfile(module_path) and os.path.splitext(module_path)[1] != ".py": continue
                if module_path.endswith(".disabled"): continue
                time_before = time.perf_counter()
                try:
                    success = reload_custom_node(module_path,  module_parent="custom_nodes")
                    
                    loadResult.append(f'{success}: {module_path}')
                except Exception as e:
                    loadResult.append(f'+++++FAILED++++: {module_path}')
                node_import_times.append((time.perf_counter() - time_before, module_path, success))

        loadResult = "\n".join(loadResult)
        return (loadResult,)#{ "ui": { "node_import_times": node_import_times } }
    
class QsListNodes:
    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                
            },
            "optional": {
                "dirs_to_list": ("STRING", {"default": "[]"}),
                "reload_value": ("INT", {"default": 0, "min": 0, "max": 100, "step": 1, "round": False}),
            },
        }
    RETURN_TYPES = ("STRING","STRING",)
    RETURN_NAMES= ('NodeList',"Node list detailed")
    FUNCTION = "list_external_custom_nodes"
    CATEGORY = "QS"
    OUTPUT_NODE = True


    
    def list_external_custom_nodes(self,dirs_to_list='[]', reload_value = 0):
       # from nodes import QS_NODE_CLASS_MAPPINGS, QS_NODE_DISPLAY_NAME_MAPPINGS, QS_EXTENSION_WEB_DIRS
        """
        Initializes the external custom nodes.

        This function loads custom nodes from the specified folder paths and imports them into the application.
        It measures the import times for each custom node and logs the results.

        Returns:
            None
        """
        print("list_external_custom_nodes", reload_value)
        #read file .nodelist.txt and return the string
        node_paths = folder_paths.get_folder_paths("custom_nodes")
        custom_node_modules = [module for node_path in node_paths for module in os.listdir(node_path) 
                                if module not in ['.git','.ipynb_checkpoints','__pycache__']]
        
        custom_node_modules_str = '\n'.join(custom_node_modules) 

        try:
            nodelist = open('qs_extras/.nodelist.txt', 'r').read()
            # replace every \n with \\n
            nodelist = nodelist.replace('\n', '\n')
            print('nodelist', nodelist)
            return (custom_node_modules_str, nodelist,)  
        except Exception as e:
            print("nodelist did not work got exception", e)    
            node_paths = folder_paths.get_folder_paths("custom_nodes")
            custom_node_modules = [module for node_path in node_paths for module in os.listdir(node_path) ]
            custom_node_modules = [f'   {module}' for module in custom_node_modules ] 
        #  custome_nodes_str = json.dumps(custom_node_modules, indent=4)
            return  (custom_node_modules_str, custom_node_modules,)
        
        

NODE_CLASS_MAPPINGS = {
    "QsAddPath": QsAddPath,
    "QsListNodes": QsListNodes,
    "QsLoadNodes": QsLoadNodes
}
NODE_DISPLAY_NAME_MAPPINGS = {
    "QsAddPath": "(Down)load CogVideo Model",
    "QsListNodes": "List External Custom Nodes",
    "QsLoadNodes": "Load External Custom Nodes"
}