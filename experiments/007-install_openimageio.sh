#!/bin/bash
#USER=<EMAIL>
#USE_PROFILE=false
#REBOOT=false


# Paths
REPO_DIR="/home/<USER>/infra"
QS_USER="<EMAIL>"

sudo -H -i -u $QS_USER bash << 'EOF'
source /opt/miniconda3/etc/profile.d/conda.sh
source ~/.bashrc  # Ensure .bashrc is sourced after conda create
conda activate qs
cd /qsfs2/services/ComfyUI/
#pip install openimageio
conda install -y conda-forge::openimageio
conda install -y -c conda-forge py-openimageio
pip install -r ./custom_nodes/qs_comfyui_nodes/requirements.txt 
EOF

echo "successfully added regular updates of pip to crontab"
