version: '3.8'

services:
  comfyui:
    image: comfy:1.0
    ports:
      - "18892:18892"
      - "18899:18899"
    volumes:
      - ./input:/qsfs2/services/ComfyUI/input
      - ./output:/qsfs2/services/ComfyUI/output
      - ./models:/qsfs2/services/ComfyUI/models
      - /tmp:/tmp
    #   - ./additional_nodes:/ComfyUI/additional_nodes
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
    command: comfy
