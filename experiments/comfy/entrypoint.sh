#!/bin/bash

set -e

#source activate comfy
nvidia-smi

case "$1" in
  comfy)
    for dir in /ComfyUI/additional_nodes/*; do
      if [ -f "$dir/requirements.txt" ]; then
        pip install -r "$dir/requirements.txt"
      fi
    done
    python main.py --multi-user --use-pytorch-cross-attention --listen 0.0.0.0 --port 18892  2>&1 >/tmp/comfy_18892.out
    ;;
  comfydebug)
    for dir in /ComfyUI/additional_nodes/*; do
      if [ -f "$dir/requirements.txt" ]; then
        pip install -r "$dir/requirements.txt"
      fi
    done
    python main.py --disable-all-custom-nodes --multi-user --use-pytorch-cross-attention --listen 0.0.0.0 --port 18899  2>&1 >/tmp/comfy_18899.out
    ;;
  *)
    exec bash
    ;;
esac
