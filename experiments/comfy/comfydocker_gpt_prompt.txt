I want to create a docker file for a product ComfyUI
The base product image to be built as 
Start with a base image qsregistry.azurecr.io/qsbase:1.0 
pull the code from 
**************:quantstac/qsComfy.git
with ssh

The base directory of the product should be installed with new user 
comfy
Install in directory 
/ComfyUI 
The /ComfyUI many directories following directories has special funtion  
 - input   
  - outout
  - models 
  These directories are for working large files and will be mounted from host machine

 - custom_nodes
 This directory contains many sub directories representing nodes. There are fixed set of nodes, they should be pulled and placed in 
 list of fixed nodes git repos are available in 
 fixed_custom_nodes.txt all of them should be cloned in custom_nodes directory

After pulling all the nodes 
create a conda environment comfy (Miniconda is already available in base image)
activate the environment 
install following 

1. pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124

2. pip install /ComfyUI/requirements.txt 
3. Then iteratively go through each directory under custom_nodes 
and install 
pip install requirements.txt  (under those directories)

Create a directory for certificates 
/certs
copy /etc/letsencrypt/qstools/certs/ files into certs

There will additional set of nodes for loading additional things these nodes are availabled on host machines  in 
 additional_nodes. 
 All the directories under additional_nodes should be mounted in custom_nodes directory 

Give the entrypoint.sh with following commands depending on the argument

1. comfy
set conda envirionment qs
That installs all the requirements.txt of additional_nodes under each diretory 
And run 
python   main.py --multi-user --use-pytorch-cross-attention --listen 0.0.0.0 --port 18892 --tls-keyfile /certs/privkey.pem --tls-certfile /certs/cert.pem 2>&1 >/tmp/comfy_18892.out

2. comfydebug
set conda envirionment qs
That installs all the requirements.txt of additional_nodes under each diretory 
And run 
python main.py --disable-all-custom-nodes --multi-user --use-pytorch-cross-attention --listen 0.0.0.0 --port 18899 --tls-keyfile /certs/privkey.pem --tls-certfile /certs/cert.pem 2>&1 >/tmp/comfy_18899.out

3.  no arguments
enter the container with shell


Also create a docker-compose file for ease
