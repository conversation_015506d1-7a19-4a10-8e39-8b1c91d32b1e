# Use the base image
FROM qs:1.0

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive

# Create a new user `comfy` and set up the working directory
RUN useradd -m comfy && \
    mkdir -p /ComfyUI && \
    chown comfy:comfy /ComfyUI  && \
    mkdir -p /certs && chown comfy:comfy /certs

WORKDIR /ComfyUI
# Switch to user `comfy`
USER comfy

# Clone the main repository using a PAT
ARG GITHUB_PAT
RUN git clone https://${GITHUB_PAT}@github.com/quantstac/qsComfy.git 
#/ComfyUI
RUN mv qsComfy/* . && rm -rf qsComfy

# Create and mount special directories
RUN mkdir -p /ComfyUI/input /ComfyUI/output /ComfyUI/models /ComfyUI/custom_nodes
# Clone fixed custom nodes listed in `fixed_custom_nodes.txt`
COPY fixed_custom_nodes.txt /ComfyUI/fixed_custom_nodes.txt
COPY fixed_private_custom_nodes.txt /ComfyUI/fixed_private_custom_nodes.txt

# cd to /ComfyUI/custom_nodes
WORKDIR /ComfyUI/custom_nodes
ARG GITHUB_PAT
RUN while read -r repo; do \
    git clone ${repo} ; \
    done < /ComfyUI/fixed_custom_nodes.txt

# now clone repose from fixed_private_custom_nodes.txt
RUN while read -r repo; do \
    git clone https://${GITHUB_PAT}@github.com/${repo} ; \
    done < /ComfyUI/fixed_private_custom_nodes.txt

WORKDIR /ComfyUI

# Set up Conda environment
#RUN conda create -n comfy python=3.12 -y && conda clean --all -y

# Install primary dependencies
# pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124 && \

RUN bash -c "\
    pip install -r /ComfyUI/requirements.txt"
# source activate comfy && 
# Install requirements for custom nodes
RUN bash -c "\
    for dir in /ComfyUI/custom_nodes/*; do \
    if [ -f \"$dir/requirements.txt\" ]; then \
    pip install -r \"$dir/requirements.txt\"; \
    fi; \
    done"

# Copy certificates
#COPY certs/* /certs/

WORKDIR /ComfyUI 
# Create the entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
USER root
RUN chmod +x /usr/local/bin/entrypoint.sh
USER comfy

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
