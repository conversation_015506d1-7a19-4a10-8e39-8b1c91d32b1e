{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"type": "Microsoft.Network/networkInterfaces", "apiVersion": "2021-02-01", "name": "qst4spot011733_z1", "location": "southcentralus", "properties": {"ipConfigurations": [{"name": "ipconfig1", "properties": {"subnet": {"id": "/subscriptions/2891bfbb-6705-490c-a09f-3fc8ff555228/resourceGroups/qsuser/providers/Microsoft.Network/virtualNetworks/qsvnet001/subnets/default"}, "publicIPAddress": {"id": "[resourceId('Microsoft.Network/publicIPAddresses', 'qst4spot012-ip')]"}, "privateIPAllocationMethod": "Dynamic"}}], "networkSecurityGroup": {"id": "/subscriptions/2891bfbb-6705-490c-a09f-3fc8ff555228/resourceGroups/qsuser/providers/Microsoft.Network/networkSecurityGroups/qslowgpu001-nsg"}, "enableAcceleratedNetworking": true}}]}