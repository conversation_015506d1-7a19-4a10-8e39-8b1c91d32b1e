#!/bin/bash

# Variables
AAD_USER="<EMAIL>"
AAD_USER_ID="5fc832b8-5c5e-4625-b28d-2c0a68ba7137"
GROUP_ADMIN="admin"
GROUP_AAD_ADMINS="aad_admins"
GROUP_ADMIN_ID="118"
GROUP_AAD_ADMINS_ID="1001"
AADPASSWD_FILE="/etc/aadpasswd"

# Function to add a user to a group
add_user_to_group() {
    local user=$1
    local group=$2
    if id -u $user >/dev/null 2>&1; then
        sudo usermod -a -G $group $user
        echo "User '$user' added to group '$group'."
    else
        echo "User '$user' does not exist."
    fi
}

# Check if group 'admin' exists, if not create it with the specified GID
if ! getent group $GROUP_ADMIN >/dev/null; then
    sudo groupadd -g $GROUP_ADMIN_ID $GROUP_ADMIN
    echo "Group '$GROUP_ADMIN' created with GID $GROUP_ADMIN_ID."
fi

# Check if group 'aad_admins' exists, if not create it with the specified GID
if ! getent group $GROUP_AAD_ADMINS >/dev/null; then
    sudo groupadd -g $GROUP_AAD_ADMINS_ID $GROUP_AAD_ADMINS
    echo "Group '$GROUP_AAD_ADMINS' created with GID $GROUP_AAD_ADMINS_ID."
fi

# Check if the user exists in /etc/aadpasswd
if ! grep -q "^$AAD_USER:" $AADPASSWD_FILE; then
    sudo aaduseradd -m -s /bin/bash -o $AAD_USER_ID $AAD_USER
    echo "AAD user '$AAD_USER' created."
else
    echo "AAD user '$AAD_USER' already exists."
fi

# Add the AAD user to the specified groups
add_user_to_group $AAD_USER $GROUP_ADMIN
add_user_to_group $AAD_USER $GROUP_AAD_ADMINS

# Verify if the user is in the groups
if groups $AAD_USER | grep &>/dev/null "\b$GROUP_ADMIN\b" && groups $AAD_USER | grep &>/dev/null "\b$GROUP_AAD_ADMINS\b"; then
    echo "User '$AAD_USER' is correctly assigned to groups '$GROUP_ADMIN' and '$GROUP_AAD_ADMINS'."
else
    echo "There was an issue adding the user '$AAD_USER' to the groups."
fi

