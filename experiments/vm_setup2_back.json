{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "resources": [{"type": "Microsoft.Compute/virtualMachines", "apiVersion": "2021-07-01", "name": "qst4spot012", "location": "southcentralus", "identity": {"type": "SystemAssigned"}, "properties": {"hardwareProfile": {"vmSize": "Standard_NC8as_T4_v3"}, "storageProfile": {"osDisk": {"createOption": "Attach", "managedDisk": {"id": "[resourceId('Microsoft.Compute/disks', 'qst4spot011_OsDisk_1')]", "storageAccountType": "Premium_LRS"}, "osType": "Linux"}}, "osProfile": {"computerName": "qst4spot012", "adminUsername": "qsuser", "linuxConfiguration": {"disablePasswordAuthentication": true, "ssh": {"publicKeys": [{"path": "/home/<USER>/.ssh/authorized_keys", "keyData": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC+9y4u+QhBUyP8ZUSL2bGTpXvZSgNxUGEn94K3QBUQ3RNbPqaVmQAYwiKIgRQOxu059ol0QTx+wu1fH5bF+U3PH11yUfDKbZnsXPihWIy5VPLi5oIGfM7in9EETpH0lW+aLanxI5sdT7aslGAOwAwvUQg3oD9ykUq4O//+3eu0CRPDH00f9og8QPF9L6wSGF7FDQF4a4MNujFKEH6jcdwQZLTydmleQ8MosoaPNssSXLCkbpKemYCNnVJvQQh+HkXh0dYfpUHndt50Xoz2EXqF32RJ8JbDhZQwVJkqrhuRM2cCU5subrMKrpi/Um4cpuUkrxmqQHsiglfwlROkqzZZrnTZIDwaKDng10yZLQipjzxeHnHL8+KyMTbB0tvGUzoI3JdEzmj7zHUUKZ6mBYsTf8gxxm5hMmly63YEPiQPcnQKYqcbCJgafAnlGF9gAHz8bmA2KFkkCaBlTHnEIb8daUkGOkQW6bAfS7nL8hQV8mnSvMLIcwuRzw4TNi94XeE= generated-by-azure"}]}, "provisionVMAgent": true}, "customData": "[base64('IyEvYmluL2Jhc2gKZWNobyAiSGVsbG8sIFdvcmxkISIgPiAvdmFyL3RtcC9oZWxsby50eHQK')]"}, "networkProfile": {"networkInterfaces": [{"id": "[resourceId('Microsoft.Network/networkInterfaces', 'qst4spot011733_z1')]"}]}, "priority": "Spot", "evictionPolicy": "Deallocate", "billingProfile": {"maxPrice": -1}, "diagnosticsProfile": {"bootDiagnostics": {"enabled": true}}}, "resources": []}], "outputs": {}}