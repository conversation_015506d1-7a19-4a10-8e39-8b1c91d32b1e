#!/bin/bash
#USER=<EMAIL>
#USE_PROFILE=false
#REBOOT=false


# Paths
REPO_DIR="/home/<USER>/infra"


# User and group
USER="<EMAIL>"
GROUP="<EMAIL>"

# Ensure the destination config directory exists
sudo mkdir -p /qsfs2/services/scripts


# Copy rclone_auth.key to the correct directory
sudo cp $REPO_DIR/comfy_pip_updates.sh /qsfs2/services/scripts/comfy_pip_updates.sh
sudo cp $REPO_DIR/comfy_one_time_pip.sh /qsfs2/services/scripts/comfy_one_time_pip.sh

# Set ownership and permissions for rclone_auth.key conf
sudo chown -R $USER:$GROUP /qsfs2/services/scripts
sudo chmod -R 777 /qsfs2/services/scripts/

#run one time pip with  qsuser 

#add regular updates to crontab modify existing crontab add new job that runs every 1 hour
#modify existing crontab add new job that runs every 1 hour
echo "adding regular updates of pip to crontab"
cat <<CRON_EOF > /tmp/qstools_cronjobs2
SHELL=/bin/bash
BASH_ENV=~/.bashrc_conda

@reboot /qsfs2/services/rclone/mount_rclone.sh 2>&1 > /tmp/rclone1.out
@reboot sleep 60 && /qsfs2/services/ComfyUI/start.sh 2>&1 > /tmp/comfyui1.out 
@reboot sleep 60 && /qsfs2/services/jupyter/start.sh 2>&1 > /tmp/jupyter1.out 
0 * * * * /qsfs2/services/scripts/comfy_pip_updates.sh 2>&1 > /tmp/comfy_pip_updates.log
CRON_EOF

sudo -u <EMAIL> crontab /tmp/qstools_cronjobs2

echo "running one time pip"
sudo -H -u <EMAIL> /qsfs2/services/scripts/comfy_one_time_pip.sh


echo "successfully added regular updates of pip to crontab"
