import os
import ast

# Path to the base directory
base_dir = "custom_nodes"

# Function to extract NODE_CLASS_MAPPINGS from a Python file
def extract_node_class_mappings(file_path):
    with open(file_path, "r") as file:
        try:
            # Parse the file content
            file_content = file.read()
            tree = ast.parse(file_content)

            # Iterate over the AST nodes to find the NODE_CLASS_MAPPINGS dictionary
            for node in tree.body:
                if isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name) and target.id == "NODE_CLASS_MAPPINGS":
                            # Extract the keys from the dictionary
                            if isinstance(node.value, ast.Dict):
                                keys = [key.s for key in node.value.keys if isinstance(key, ast.Str)]
                                return keys
        except Exception as e:
            print(f"Error processing file {file_path}: {e}")
    return []

# Dictionary to store the results
results = {}

# Walk through the directories under custom_nodes
for root, dirs, files in os.walk(base_dir):
    # Get the top-level directory name (e.g., A, B)
    relative_path = os.path.relpath(root, base_dir)
    top_dir = relative_path.split(os.sep)[0]
    
    # Initialize the list of node classes for the top-level directory if not present
    if top_dir not in results:
        results[top_dir] = []
    
    for file in files:
        if file.endswith(".py"):
            file_path = os.path.join(root, file)
            # Extract the keys from NODE_CLASS_MAPPINGS
            node_classes = extract_node_class_mappings(file_path)
            results[top_dir].extend(node_classes)

# Helper function to format node list
def format_nodes(nodes, per_line=3):
    formatted = []
    for i in range(0, len(nodes), per_line):
        # Join nodes in groups of 'per_line'
        formatted.append(", ".join(f'"{node}"' for node in nodes[i:i + per_line]))
    sttr = "\n                     ".join(formatted)
    return f"{sttr}\n------------------------------------"

# Print the results
print(f"{'Directory':<20} {'Nodes':<50}")
print("="*70)
for dir_name, nodes in results.items():
    if nodes:
        nodes_str = format_nodes(nodes)
        print(f"{dir_name:<20} {nodes_str}")
