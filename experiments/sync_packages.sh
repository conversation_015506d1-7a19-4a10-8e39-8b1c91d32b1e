#!/bin/bash

# Define paths
ENV_NAME="qs"
CONDA_PATH="/opt/miniconda3"
CONDA_ENV_PATH="/home/<USER>/.conda/envs/$ENV_NAME"
MASTER_CONDA_LIST="/qsfs2/services/ComfyUI/master_conda_list.txt"
MASTER_PIP_LIST="/qsfs2/services/ComfyUI/master_pip_list.txt"
TEMP_CONDA_LIST="/tmp/temp_conda_list.txt"
TEMP_PIP_LIST="/tmp/temp_pip_list.txt"
FINAL_CONDA_LIST="/tmp/final_conda_list.txt"
FINAL_PIP_LIST="/tmp/final_pip_list.txt"
CURRENT_CONDA_LIST="/tmp/current_conda_list.txt"
CURRENT_PIP_LIST="/tmp/current_pip_list.txt"
MANUAL_CONDA_LIST_FILE="/qsfs2/services/ComfyUI/manual_conda_list.txt"
MANUAL_PIP_LIST_FILE="/qsfs2/services/ComfyUI/manual_pip_list.txt"

# Activate the conda environment
echo "Activating conda environment: $ENV_NAME"
eval "$(conda shell.bash hook)" && conda activate qs

# 1. Generate current conda package list (excluding those installed via pip)
echo "Generating current conda package list..."
conda list --export | grep -v "pypi" > "$CURRENT_CONDA_LIST"
echo "Current conda package list saved to $CURRENT_CONDA_LIST"

# 2. Generate current pip package list, including entries installed via URLs or files
echo "Generating current pip package list..."
pip freeze > "$CURRENT_PIP_LIST"
echo "Current pip package list saved to $CURRENT_PIP_LIST"

# 3. Merge and update the master conda package list
echo "Updating master conda package list..."                                                                       
cat "$MASTER_CONDA_LIST" "$CURRENT_CONDA_LIST" | sort | uniq > "$FINAL_CONDA_LIST"
echo "Updated master conda package list saved to $FINAL_CONDA_LIST"

# 4. Merge and update the master pip package list
echo "Updating master pip package list..."
cat "$MASTER_PIP_LIST" "$CURRENT_PIP_LIST" | sort | uniq > "$FINAL_PIP_LIST"
echo "Updated master pip package list saved to $FINAL_PIP_LIST"

echo "Installing missing conda packages..."
while IFS= read -r package; do
    if grep -q $(echo "$package" | cut -d'=' -f1 | cut -d' ' -f1) "$MANUAL_CONDA_LIST_FILE"; then
        echo "conda $package is to be manually installed"
    elif grep -q "^$package$" "$CURRENT_CONDA_LIST"; then
        echo "conda $package is already installed"
    else
        echo "Installing conda package: $package"
        conda install --yes "$package"
    fi
done < "$FINAL_CONDA_LIST"

# 6. Install missing pip packages with correct versions
echo "Installing missing pip packages..."
while IFS= read -r package; do
    package_name=$(echo "$package" | cut -d'=' -f1 | cut -d' ' -f1)
    if grep -q "^$package_name==" "$MANUAL_PIP_LIST_FILE"; then
        echo "pip $package is to be manually installed"
    elif echo "$package" | grep -q '@'; then
        # Handle packages installed via URL or local path
        if grep -qi "^$package_name" "$CURRENT_PIP_LIST"; then
            echo "pip $package already installed"
        else
            echo "Installing pip package from URL/local path: $package"
            pip install "$package"
        fi
    else
        # Handle standard packages
        if grep -qi "^$package_name==" "$CURRENT_PIP_LIST"; then
            echo "pip $package already installed"
        else
            echo "Installing pip package: $package"
            pip install "$package"
        fi
    fi
done < "$FINAL_PIP_LIST"

# 7. Update master lists with any new packages installed
echo "Updating master package lists..."
cp "$FINAL_CONDA_LIST" "$MASTER_CONDA_LIST"
cp "$FINAL_PIP_LIST" "$MASTER_PIP_LIST"
echo "Master package lists updated."

# Clean up temporary files
echo "Cleaning up temporary files..."
rm -f "$TEMP_CONDA_LIST" "$TEMP_PIP_LIST" "$FINAL_CONDA_LIST" "$FINAL_PIP_LIST" "$CURRENT_CONDA_LIST" "$CURRENT_PIP_LIST"
echo "Temporary files cleaned up."

echo "Package synchronization script completed."

