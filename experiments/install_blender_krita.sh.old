#!/bin/bash

# Usage: ./install_blender_krita.sh
# if [ "$EUID" -ne 0 ]
#   then echo "Please run as root"
#   exit
# fi

echo "Updating system and installing dependencies..."
sudo apt update
sudo apt upgrade -y
sudo apt install -y x2goserver x2goserver-xsession libglu1-mesa dbus-x11 wget software-properties-common x11vnc

#echo "Adding NVIDIA package repositories..."
#sudo apt-key adv --fetch-keys https://developer.download.nvidia.com/compute/cuda/repos/ubuntu1804/x86_64/7fa2af80.pub
#sudo sh -c 'echo "deb https://developer.download.nvidia.com/compute/cuda/repos/ubuntu1804/x86_64 /" > /etc/apt/sources.list.d/cuda.list'

#echo "Installing NVIDIA driver and CUDA toolkit..."
#sudo apt update
#sudo apt install -y nvidia-driver-460 cuda

echo "Enabling X2Go server to start on boot..."
#sudo systemctl enable x2goserver

# Create a script to start Xvfb
echo -e '#!/bin/bash\nXvfb :1 -screen 0 1920x1080x24 &\nexport DISPLAY=:1\n' > start_xvfb.sh
chmod +x start_xvfb.sh

# Set VNC password
x11vnc -storepasswd

# Create a script to start x11vnc
echo -e '#!/bin/bash\nx11vnc -display :1 -forever -auth guess -passwd your_vnc_password\n' > start_vnc.sh
chmod +x start_vnc.sh

#echo "Rebooting to load NVIDIA drivers and complete setup..."
#sudo reboot now

# Wait for reboot to complete
#sleep 60

echo "Downloading and installing Blender..."
wget https://download.blender.org/release/Blender3.0/blender-3.0.0-linux-x64.tar.xz
tar -xvf blender-3.0.0-linux-x64.tar.xz
sudo mv blender-3.0.0-linux-x64 /opt/blender

echo "Adding Krita PPA and installing Krita..."
sudo add-apt-repository ppa:kritalime/ppa -y
sudo apt update
sudo apt install -y krita
sudo ln /opt/blender/bin/blender /usr/bin/blender

echo "Installation and setup complete. X2Go server is configured to start on boot. Users can connect via X2Go client and start Blender or Krita manually."
