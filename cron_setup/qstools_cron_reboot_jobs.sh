#!/bin/bash
SHELL=/bin/bash
BASH_ENV=~/.bashrc_conda
acrAppId="f29266b4-f797-424d-897c-fd4644b1e518"
acrPwd="****************************************"
acrTenant="73b55c4d-0679-4a00-acbf-3ef8759d1c31"

echo "Running rclone mount" 2>&1 >> /tmp/qstools_cron_reboot_jobs.log 
sudo mkdir -p /home/<USER>/.config/rclone && sudo chown -R <EMAIL>:<EMAIL> /home/<USER>/.config && \
nohup /qsfs2/services/rclone/mount_rclone.sh 2>&1 >> /tmp/qstools_cron_reboot_jobs.log &
disown

echo "Running comfy docker" 2>&1 >> /tmp/qstools_cron_reboot_jobs.log 
cd /qsfs2/services/qsComfy && bash ./qs_mount.sh 2>&1  > /tmp/comfy_docker.out && \
az login --service-principal --username $acrAppId --password $acrPwd --tenant $acrTenant 2>&1  >> /tmp/comfy_docker.out && \
sleep 2 && az acr login --name qsacr001 && docker pull qsacr001.azurecr.io/qs-comfy:latest 2>&1  >> /tmp/comfy_docker.out && \
HOSTNAME=$HOSTNAME docker compose up -d 2>&1  >> /tmp/comfy_docker.out 

echo "Running local developer comfy server" 2>&1 >> /tmp/qstools_cron_reboot_jobs.log
sleep 2 && cd /qsfs2/services/qsComfy && bash ./local_comfy_dev_boot.sh 2>&1 > /tmp/comfy_dev.out 

echo "Running jupyter"  2>&1 >> /tmp/qstools_cron_reboot_jobs.log
sleep 2 && /qsfs2/services/infra/jupyter/ad_start_jupyter.sh 2>&1 > /tmp/jupyter.cron.out 

echo "Running filebrowser" 2>&1 >> /tmp/qstools_cron_reboot_jobs.log
sudo mkdir -p /home/<USER>/.config/filebrowser && sudo chown -R <EMAIL>:<EMAIL> /home/<USER>/.config && \
sleep 2 &&  cd /qsfs2/services/infra/docker_scripts/filebrowser && cat /etc/passwd /etc/aadpasswd > ./passwd && docker compose up -d 2>&1 > /tmp/filebrowser.cron.out

echo "Running nginx" 2>&1 >> /tmp/qstools_cron_reboot_jobs.log
sleep 2 &&  cd /qsfs2/services/infra/docker_scripts/proxy/ &&  HOSTNAME=$HOSTNAME docker compose up -d 2>&1 > /tmp/nginx.cron.out

