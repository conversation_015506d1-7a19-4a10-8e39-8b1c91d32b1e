#!/bin/bash

# Default values
TARGET_DIR="`pwd`/custom_nodes"
NODE_DIR=""

# Parse arguments
if [ $# -ge 1 ]; then
  NODE_DIR="$1"
fi

# Helper function to unmount a directory
unmount_and_cleanup() {
  local dir="$1"
  echo "Unmounting $dir..."
  sudo fusermount -u "$dir" 2>/dev/null
  if [ $? -eq 0 ]; then
    echo "Successfully unmounted $dir"
    # Check if the directory is empty and delete it
    if [ -d "$dir" ] && [ -z "$(ls -A "$dir")" ]; then
      echo "Directory $dir is empty. Deleting it..."
      rmdir  "$dir"
    fi
  else
    echo "Failed to unmount $dir or it was not mounted"
  fi
}

# Unmount specific node directory
if [ -n "$NODE_DIR" ]; then
  NODE_PATH="$TARGET_DIR/$NODE_DIR"
  if [ -d "$NODE_PATH" ]; then
    unmount_and_cleanup "$NODE_PATH"
  else
    echo "Error: Directory $NODE_PATH does not exist"
    exit 1
  fi

# Unmount all FUSE mounts in the target directory
else
  echo "Unmounting all FUSE mounts in $TARGET_DIR..."
  grep "fuse " /proc/mounts | awk -v target="$TARGET_DIR" '$2 ~ "^"target {print $2}' | while read -r mount_point; do
    unmount_and_cleanup "$mount_point"
  done
fi
