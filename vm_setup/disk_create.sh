#!/bin/bash

# Check if the argument is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <disk_prefix>"
  exit 1
fi

# Variables
VM_NAME="$1"
RESOURCE_GROUP="qsuser"
DISK_NAME="${VM_NAME}_OsDisk_1"
LOCATION="southcentralus"
ZONE="1"
SKU="Premium_LRS"
OS_TYPE="Linux"
HYPERV_GENERATION="V2"
IMAGE_ID="/Subscriptions/2891bfbb-6705-490c-a09f-3fc8ff555228/Providers/Microsoft.Compute/Locations/southcentralus/Publishers/canonical/ArtifactTypes/VMImage/Offers/0001-com-ubuntu-server-jammy/Skus/22_04-lts-gen2/Versions/22.04.202405140"
DISK_SIZE_GB=256
TEMP_JSON_FILE="/tmp/${VM_NAME}_disk.json"

# Create the disk JSON configuration file
cat <<EOF > ${TEMP_JSON_FILE}
{
  "\$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "*******",
  "resources": [
    {
      "type": "Microsoft.Compute/disks",
      "apiVersion": "2021-04-01",
      "name": "$DISK_NAME",
      "location": "$LOCATION",
      "zones": ["$ZONE"],
      "sku": {
        "name": "$SKU"
      },
      "properties": {
        "osType": "$OS_TYPE",
        "hyperVGeneration": "$HYPERV_GENERATION",
        "creationData": {
          "createOption": "FromImage",
          "imageReference": {
            "id": "$IMAGE_ID"
          }
        },
        "diskSizeGB": $DISK_SIZE_GB,
        "encryption": {
          "type": "EncryptionAtRestWithPlatformKey"
        }
      }
    }
  ]
}
EOF

# Execute the deployment
az deployment group create --resource-group $RESOURCE_GROUP --template-file ${TEMP_JSON_FILE}
# Clean up
rm -f ${TEMP_JSON_FILE}

echo "Disk creation completed."
exit 0
