#!/bin/bash

if [ $# -ne 2 ]; then
    echo "Usage: $0 requirements.txt env"
    exit 1
fi

req_file="$1"
conda_env="$2"

# Activate the conda environment
source activate $conda_env

# Get the list of installed packages and their versions in the conda environment
conda list --export > installed_packages.txt

# Print the table header
printf "%-20s %-15s %-20s %-10s\n" "Package" "Req Version" "Installed Version" "Status"
printf "%-20s %-15s %-20s %-10s\n" "-------" "-----------" "-----------------" "------"

# Read the requirements.txt file
while IFS= read -r line; do
    # Extract package name and version from requirements.txt
    req_package=$(echo $line | cut -d'=' -f1)
    #if req_package ends with > or < trim it
    req_package=$(echo $req_package | sed 's/>$//g' | sed 's/<$//g')
    req_version=$(echo $line | cut -d'=' -f3)
    if [[ -z $req_version ]]; then
        req_version=$(echo $line | cut -d'=' -f2)
    fi

    # Search for the package in the installed packages list
    installed_info=$(grep "^$req_package" installed_packages.txt)

    if [[ -n $installed_info ]]; then
        # Extract installed package version
        installed_version=$(echo $installed_info | awk -F= '{print $2}')

        # Compare versions
        if [[ "$req_version" == "$installed_version" ]]; then
            status="Equal"
        elif [[ "$req_version" > "$installed_version" ]]; then
            status="Lower"
        else
            status="Higher"
        fi
    else
        installed_version="Not Installed"
        status="Not Installed"
    fi

    # Print the results
    printf "%-20s %-15s %-20s %-10s\n" "$req_package" "$req_version" "$installed_version" "$status"
done < "$req_file"

# Cleanup
