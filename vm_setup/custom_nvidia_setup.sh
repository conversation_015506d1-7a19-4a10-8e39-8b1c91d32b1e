#!/bin/bash

echo "started the nvidia process"

# Function to kill hanging apt processes and remove lock files
clean_apt_locks() {
    echo "Checking and terminating any active apt processes..."
    if pgrep -x "apt" > /dev/null || pgrep -x "apt-get" > /dev/null || pgrep -x "dpkg" > /dev/null; then
        killall -9 apt apt-get dpkg || true
    fi
    echo "Removing lock files..."
    rm -f /var/lib/dpkg/lock-frontend
    rm -f /var/lib/dpkg/lock
    echo "Reconfiguring dpkg..."
    dpkg --configure -a
}

# Clean any existing apt locks and terminate hanging processes
clean_apt_locks

# Fetch and add the NVIDIA public keys for CUDA and machine learning repositories
echo "Fetching and adding NVIDIA public keys..."
apt-key adv --fetch-keys https://developer.download.nvidia.com/compute/cuda/repos/ubuntu1804/x86_64/3bf863cc.pub
apt-key adv --fetch-keys https://developer.download.nvidia.com/compute/machine-learning/repos/ubuntu1804/x86_64/7fa2af80.pub

# Update package lists
echo "Updating package lists..."
DEBIAN_FRONTEND=noninteractive apt update -y

# Upgrade all packages non-interactively
echo "Upgrading packages..."
DEBIAN_FRONTEND=noninteractive apt upgrade -y

# Upgrade all packages again to ensure everything is up to date
echo "Performing full upgrade..."
DEBIAN_FRONTEND=noninteractive apt full-upgrade -y

# Clean any apt locks and terminate hanging processes again after the upgrade
clean_apt_locks

echo "checking nvidia setup"
nvidia-smi

echo "++_ System upgrade of nvidia custom setup completed successfully. Move to next setup +++"

