
[Unit]
Description=Mount Microsoft Teams Files using Rclone
After=network-online.target

[Service]
Type=simple
Environment=RCLONE_CONFIG=/home/<USER>/.config/rclone/rclone.conf
ExecStart=sudo -H -u <EMAIL> rclone --vfs-cache-mode writes mount msteams: /home/<USER>/teams_files
ExecStop=sudo -H -u <EMAIL> /bin/fusermount3 -u /home/<USER>/teams_files
Restart=always
User=<EMAIL>
Group=<EMAIL>

[Install]
WantedBy=default.target