#!/bin/bash

# Check if the script has received an argument for VM_NAME
if [ -z "$1" ]; then
  echo "Usage: $0 <VM_NAME>"
  exit 1
fi

VM_NAME="$1"
vmSize="Standard_NC4as_T4_v3"

# Function to confirm or change VM_NAME
confirm_vmName() {
  while true; do
    read -r -p "You are using VM_NAME: $VM_NAME. Press [y] to confirm, [c] to change, [n] to discontinue: " choice
    
    case $choice in
      y|Y)
        echo "You have confirmed VM_NAME: $VM_NAME."
        break
        ;;
      c|C)
        read -r -p "Enter the new VM_NAME: " new_vmName
        VM_NAME="$new_vmName"
        ;;
      n|N)
        echo "Discontinued by the user."
        exit 0
        ;;
      *)
        echo "Invalid choice. Please press y to confirm, c to change, n to discontinue."
        ;;
    esac
  done
}

# Function to choose vmSize
choose_vmSize() {
  while true; do
    echo "Choose the vmSize:"
    echo "1) Standard_NC4as_T4_v3"
    echo "2) Standard_NC24ads_A100_v4"
    echo "3) Standard_NC48ads_A100_v4"
    echo "4) Standard_NC40ads_H100_v5"
    read -r -p "Enter your choice [1-4]: " choice
    
    case $choice in
      1)
        vmSize="Standard_NC4as_T4_v3"
        read -r -p "You have chosen vmSize: $vmSize. Press [y] to confirm, any other key to choose again: " confirm_choice
        
        if [ "$confirm_choice" == "y" ] || [ "$confirm_choice" == "Y" ]; then
          echo "You have confirmed vmSize: $vmSize."
          break
        fi
        ;;
      2)
        vmSize="Standard_NC24ads_A100_v4"
        read -r -p "You have chosen vmSize: $vmSize. Press [y] to confirm, any other key to choose again: " confirm_choice
        
        if [ "$confirm_choice" == "y" ] || [ "$confirm_choice" == "Y" ]; then
          echo "You have confirmed vmSize: $vmSize."
          break
        fi
        ;;
      3)
        vmSize="Standard_NC48ads_A100_v4"
        read -r -p "You have chosen vmSize: $vmSize. Press [y] to confirm, any other key to choose again: " confirm_choice
        
        if [ "$confirm_choice" == "y" ] || [ "$confirm_choice" == "Y" ]; then
          echo "You have confirmed vmSize: $vmSize."
          break
        fi
        ;;
      4)
        vmSize="Standard_NC40ads_H100_v5"
        read -r -p "You have chosen vmSize: $vmSize. Press [y] to confirm, any other key to choose again: " confirm_choice
        
        if [ "$confirm_choice" == "y" ] || [ "$confirm_choice" == "Y" ]; then
          echo "You have confirmed vmSize: $vmSize."
          break
        fi
        ;;
      *)
        echo "Invalid choice. Please enter a number between 1 and 4."
        ;;
    esac
  done
}

choose_instance() {
  while true; do
    echo "Choose the instance type:"
    echo "1) Spot"
    echo "2) Full on-demand"
    read -r -p "Enter your choice [1-2]: " choice
    
    instanceType="Spot"
    case $choice in
      1)
        instanceType="Spot"
        read -r -p "You have chosen instanceType: $instanceType. Press [y] to confirm, any other key to choose again: " confirm_choice
        
        if [ "$confirm_choice" == "y" ] || [ "$confirm_choice" == "Y" ]; then
          echo "You have confirmed instanceType: $instanceType."
          break
        fi
        ;;
      2)
        instanceType="Full"
        read -r -p "You have chosen instanceType: $instanceType. Press [y] to confirm, any other key to choose again: " confirm_choice
        
        if [ "$confirm_choice" == "y" ] || [ "$confirm_choice" == "Y" ]; then
          echo "You have confirmed instanceType: $instanceType."
          break
        fi
        ;;
      *)
        echo "Invalid choice. Please enter 1 or 2."
        ;;
    esac
  done
}

# Confirm VM_NAME
confirm_vmName

# Choose vmSize
choose_vmSize

choose_instance
# Choose maxPrice
#choose_maxPrice
maxPrice=-1
echo "Hardcoded maxPrice: $maxPrice"

# Rest of your script using the confirmed VM_NAME and chosen vmSize
echo "Proceeding with VM_NAME: $VM_NAME, vmSize: $vmSize instanceType: $instanceType with maxPrice: $maxPrice"
# Add the rest of your script logic here

# Run scripts sequentially
bash ./disk_create.sh $VM_NAME
echo "Disk creation completed."

bash ./network_create.sh $VM_NAME
echo "Network creation completed."

bash ./vm_create.sh $VM_NAME $vmSize $instanceType $maxPrice
echo "VM creation completed."

echo "Machnie creation completed. \
Then login and initialize your machine"

# Do the manual alias addition step in portal
#   a. Go to dns zones -> onazure.quantstac.com -> add an A record ->  
#   b. name = ${VM_NAME} Alias record set = y, TTL 1 seconds, select Azure record ${VM_NAME}PublicIP. 
