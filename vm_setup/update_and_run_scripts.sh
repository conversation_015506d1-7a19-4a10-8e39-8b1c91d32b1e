#!/bin/bash

# Check if VM name is provided
if [ -z "$1" ]; then
    echo "Usage: bash update_and_run_scripts.sh <VM_NAME>"
    exit 1
fi

# Variables
VM_NAME=$1
GIT_REPO_DIR="/home/<USER>/infra"
SCRIPTS_DIR="$GIT_REPO_DIR/one_time_run_scripts"
LOG_DIR="/qsfs2/one_time_script_logs/$VM_NAME"
LOG_FILE="$LOG_DIR/script_update.log"
TEMP_LOG_FILE="$LOG_DIR/temp_script_update.log"

# Pull the latest changes from the Git repository
cd "$GIT_REPO_DIR" || exit
sudo -u <EMAIL> git pull

# Ensure the log directory exists
sudo mkdir -p "$LOG_DIR"

# copy existing log file to logfiles.last if it exists
if [ -f "$LOG_FILE" ]; then
    sudo cp "$LOG_FILE" "$LOG_DIR/script_update_last.log"
fi
# Clear the log file and create a new temporary log file
> "$LOG_FILE"
> "$TEMP_LOG_FILE"

# Function to run scripts with different users and profiles
run_script() {
    local script=$1
    local user=$2
    local use_profile=$3
    local vm_name=$4
    echo "running script $script with user $user and use_profile $use_profile"
    
    if [ "$use_profile" == "true" ]; then
        sudo -i -u "$user" VM_NAME="$vm_name" bash "$script" 
    else
        sudo -u "$user" VM_NAME="$vm_name" bash "$script" 
    fi
}

# Check for new scripts
DO_REBOOT="false"
for script in "$SCRIPTS_DIR"/*.sh; do
    LAST_RUN_FILE="$LOG_DIR/$(basename "$script").last_run"
    if [ ! -f "$LAST_RUN_FILE" ]; then
        echo "0" > "$LAST_RUN_FILE"
    fi

    LAST_RUN=$(cat "$LAST_RUN_FILE")
    SCRIPT_MODIFIED_TIME=$(stat -c %Y "$script")

    if [ -z "$LAST_RUN" ]; then
        LAST_RUN=0
    fi

    if [ "$SCRIPT_MODIFIED_TIME" -gt "$LAST_RUN" ]; then
        echo "Script $script has been modified since last run." 
        echo "Script $script has been modified since last run." >> "$LOG_FILE"
        # Get user and use_profile info from the script header (e.g., #USER=user #USE_PROFILE=true)
        USER=$(grep -oP '(?<=#USER=).+' "$script")
        USE_PROFILE=$(grep -oP '(?<=#USE_PROFILE=).+' "$script")
        REBOOT=$(grep -oP '(?<=#REBOOT=).+' "$script")
        echo "Running script $script as $USER with use_profile=$USE_PROFILE"
        echo "Running script $script as $USER with use_profile=$USE_PROFILE" >> "$LOG_FILE"
        if [ "$USER" == "root" ]; then
            echo "Running script $script as root"
            echo "Running script $script as root" >> "$LOG_FILE"
            VM_NAME="$VM_NAME" bash "$script"
        else
            echo "Running script $script as $USER"
            echo "Running script $script as $USER" >> "$LOG_FILE"
            run_script "$script" "$USER" "$USE_PROFILE" "$VM_NAME" 
        fi

        # Check the success status
        if [ $? -eq 0 ]; then
            # Update the last run timestamp if successful
            date +%s > "$LAST_RUN_FILE"
            echo "$(date): Successfully ran $script"
            echo "$(date): Successfully ran $script" >> "$LOG_FILE"
            echo "success" >> "$TEMP_LOG_FILE"
            if [ "$REBOOT" == "true" ]; then
                DO_REBOOT="true"
            fi
        else
            echo "$(date): Failed to run $script"
            echo "$(date): Failed to run $script" >> "$LOG_FILE"
        fi
    else
        echo "Skipping $script as it was not modified since last run"
        echo "Skipping $script as it was already run" >> "$LOG_FILE"
    fi
done

# Check the temporary log file for successful runs
if [ "$DO_REBOOT" == "true" ]; then
    echo "Rebooting the machine"
    echo "Rebooting the machine" >> "$LOG_FILE"
    sudo reboot
fi
