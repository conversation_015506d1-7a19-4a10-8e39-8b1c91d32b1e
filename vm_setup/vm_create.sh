#!/bin/bash

# Function to check the exit code of the previous command
check_error() {
  if [ "$?" -ne 0 ]; then
    echo "Error: Previous command failed. Exiting."
    exit 1
  fi
}

# Function to check if a VM exists
vm_exists() {
  az vm show --resource-group "$resourceGroup" --name "$VM_NAME" --query "provisioningState" -o tsv > /dev/null 2>&1
  return $?
}

# Function to check if a user exists on the VM (requires VM to be running and accessible)
user_exists() {
  local user="$1"
  # Use a more reliable way to check for user.  This assumes you have passwordless sudo setup.
  # If you don't have passwordless sudo, you'll need to adjust this command.
  az vm run-command --resource-group "$resourceGroup" --name "$VM_NAME" --command-id runShellScript --scripts "id -u $user" > /dev/null 2>&1
  return $?
}

# Function to check if boot diagnostics is enabled
boot_diagnostics_enabled() {
  az vm boot-diagnostics get-boot-log --resource-group "$resourceGroup" --name "$VM_NAME" > /dev/null 2>&1
  return $?
}

# Function to check if a VM extension exists
extension_exists() {
  local extension_name="$1"
  az vm extension show --resource-group "$resourceGroup" --vm-name "$VM_NAME" --name "$extension_name" > /dev/null 2>&1
  return $?
}

# Function to check if auto-shutdown is enabled and set to the correct time
auto_shutdown_enabled() {
  local expected_time="17:30" # 11:00 PM IST is 17:30 UTC
  local actual_time=$(az vm auto-shutdown show --resource-group "$resourceGroup" --name "$VM_NAME" --query "properties.time" -o tsv)
  if [ -n "$actual_time" ] && [ "$actual_time" == "$expected_time" ]; then
    return 0 # Auto-shutdown is enabled and time matches
  else
    return 1 # Auto-shutdown is either not enabled or time doesn't match
  fi
}

# Check if the script has received an argument
if [ -z "$1" ]; then
  echo "Usage: $0 <VM_NAME> <vmSize> <instanceType> <maxPrice>"
  exit 1
fi
check_error

if [ -z "$2" ]; then
  echo "Usage: $0 <VM_NAME> <vmSize>  <instanceType> <maxPrice>"
  exit 1
fi
check_error

if [ -z "$3" ]; then
  echo "Usage: $0 <VM_NAME> <vmSize>  <instanceType> <maxPrice>"
  exit 1
fi
check_error

if [ -z "$4" ]; then
  echo "Usage: $0 <VM_NAME> <vmSize>  <instanceType> <maxPrice>"
  exit 1
fi
check_error

VM_NAME="$1"
vmSize="$2"
instanceType="$3"
maxPrice="$4"

resourceGroup="qsuser"
location="southcentralus"
#vmSize="Standard_NC4as_T4_v3"
adminUsername="qsuser"
imagePublisher="canonical"
imageOffer="0001-com-ubuntu-server-jammy"
imageSku="22_04-lts-gen2"
networkInterfaceId="/subscriptions/2891bfbb-6705-490c-a09f-3fc8ff555228/resourceGroups/qsuser/providers/Microsoft.Network/networkInterfaces/${VM_NAME}733_z1"
osDiskId="/subscriptions/2891bfbb-6705-490c-a09f-3fc8ff555228/resourceGroups/QSUSER/providers/Microsoft.Compute/disks/${VM_NAME}_OsDisk_1"
sshPublicKey="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC+9y4u+QhBUyP8ZUSL2bGTpXvZSgNxUGEn94K3QBUQ3RNbPqaVmQAYwiKIgRQOxu059ol0QTx+wu1fH5bF+U3PH11yUfDKbZnsXPihWIy5VPLi5oIGfM7in9EETpH0lW+aLanxI5sdT7aslGAOwAwvUQg3oD9ykUq4O//+3eu0CRPDH00f9og8QPF9L6wSGF7FDQF4a4MNujFKEH6jcdwQZLTydmleQ8MosoaPNssSXLCkbpKemYCNnVJvQQh+HkXh0dYfpUHndt50Xoz2EXqF32RJ8JbDhZQwVJkqrhuRM2cCU5subrMKrpi/Um4cpuUkrxmqQHsiglfwlROkqzZZrnTZIDwaKDng10yZLQipjzxeHnHL8+KyMTbB0tvGUzoI3JdEzmj7zHUUKZ6mBYsTf8gxxm5hMmly63YEPiQPcnQKYqcbCJgafAnlGF9gAHz8bmA2KFkkCaBlTHnEIb8daUkGOkQW6bAfS7nL8hQV8mnSvMLIcwuRzw4TNi94XeE= generated-by-azure"
# Create the VM with required configurations
#check the instance type if Spot then create extra arguments
extraArgs="--enable-agent"
if [ "$instanceType" == "Spot" ]; then
  extraArgs="--enable-agent --priority Spot --eviction-policy Deallocate --max-price $maxPrice"
fi

echo "Checking if VM '$VM_NAME' exists..."
if vm_exists; then
  echo "VM '$VM_NAME' already exists."
else
  echo "Creating vm with az vm create command..."
  az vm create \
    --resource-group $resourceGroup \
    --name $VM_NAME \
    --location $location \
    --nics $networkInterfaceId \
    --attach-os-disk $osDiskId \
    --size $vmSize \
    --os-type Linux \
    --zone 1 \
    --assign-identity \
    ${extraArgs}
  check_error

  echo "VM creation completed. Starting the VM..."
  # Explicitly start the VM
  az vm start --resource-group "$resourceGroup" --name "$VM_NAME"
  check_error
fi

echo "Waiting for VM '$VM_NAME' to be in running state..."
while true; do
  state=$(az vm get-instance-view --resource-group "$resourceGroup" --name "$VM_NAME" --query "instanceView.statuses[?starts_with(code, 'PowerState')].displayStatus" -o tsv)
  check_error
  if [ "$state" == "VM running" ]; then
    echo "VM is now running."
    break
  else
    echo "VM state: $state. Waiting 10 seconds..."
    sleep 10
  fi
done

echo "Checking if user '$adminUsername' exists..."
# Note: Checking for user existence reliably requires the VM to be running and accessible.
if user_exists "$adminUsername"; then
  echo "User '$adminUsername' already exists."
else
  echo "Adding the default user '$adminUsername'..."
  #add the default user qsuser
  az vm user update \
  --resource-group $resourceGroup \
  --name $VM_NAME \
  --username $adminUsername \
  --ssh-key-value "$sshPublicKey"
  check_error
  echo "User addition completed."
fi

echo "Checking if boot diagnostics is enabled..."
if boot_diagnostics_enabled; then
  echo "Boot diagnostics is already enabled."
else
  echo "Enabling boot diagnostics..."
  # Enable boot diagnostics with managed storage account
  az vm boot-diagnostics enable \
    --resource-group $resourceGroup \
    --name $VM_NAME
  check_error
  echo "Boot diagnostics enabled."
fi

echo "Checking if AADSSHLoginForLinux extension is installed..."
if extension_exists "AADSSHLoginForLinux"; then
  echo "AADSSHLoginForLinux extension is already installed."
else
  echo "Installing AADSSHLoginForLinux extension..."
  az vm extension set \
    --resource-group $resourceGroup \
    --vm-name $VM_NAME \
    --name AADSSHLoginForLinux \
    --publisher Microsoft.Azure.ActiveDirectory \
    --version 1.0
  check_error
  echo "AADSSHLoginForLinux extension installed."
fi

echo "Checking if NvidiaGpuDriverLinux extension is installed..."
if extension_exists "NvidiaGpuDriverLinux"; then
  echo "NvidiaGpuDriverLinux extension is already installed."
else
  echo "Installing NvidiaGpuDriverLinux extension..."
  # Install NvidiaGpuDriverLinux extension
  az vm extension set \
    --resource-group $resourceGroup \
    --vm-name $VM_NAME \
    --name NvidiaGpuDriverLinux \
    --publisher Microsoft.HpcCompute \
    --version 1.9
  check_error
  echo "NvidiaGpuDriverLinux extension installed."
fi

echo "Checking if auto-shutdown policy is set..."
if auto_shutdown_enabled; then
  echo "Auto-shutdown policy is already set."
else
  echo "Setting auto-shutdown policy..."
  # Set auto-shutdown policy to 11:00 PM IST
  az vm auto-shutdown \
    --resource-group $resourceGroup \
    --name $VM_NAME \
    --time 17:30  # 11:00 PM IST is 17:30 UTC
  check_error
  echo "Auto-shutdown policy set."
fi

echo "Script execution completed."
exit 0