#!/bin/bash

# set -e  # Exit immediately if a command exits with a non-zero status
# set -o pipefail  # Ensure pipeline failures propagate
# set -u  # Treat unset variables as an error
#references used for nvidia container 
#https://www.server-world.info/en/note?os=Ubuntu_22.04&p=nvidia&f=2
#for docker 
# https://www.digitalocean.com/community/tutorials/how-to-install-and-use-docker-on-ubuntu-22-04

echo "Started the Docker and NVIDIA container setup process"

# Function to kill hanging apt processes and remove lock files
clean_apt_locks() {
    echo "Checking and terminating any active apt processes..."
    if pgrep -x "apt" > /dev/null || pgrep -x "apt-get" > /dev/null || pgrep -x "dpkg" > /dev/null; then
        sudo killall -9 apt apt-get dpkg || true
    fi
    echo "Removing lock files..."
    sudo rm -f /var/lib/dpkg/lock-frontend
    sudo rm -f /var/lib/dpkg/lock
    echo "Reconfiguring dpkg..."
    sudo dpkg --configure -a
}

# Clean any existing apt locks and terminate hanging processes
clean_apt_locks

tools_user="<EMAIL>"
MSADMIN_USER="<EMAIL>"


# Fetch and add the Docker GPG key and repository
echo "Setting up Docker repository..."
sudo apt-get update -y
sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common
#curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor > /tmp/docker-archive-keyring.gpg
sudo mv -f /tmp/docker-archive-keyring.gpg /usr/share/keyrings/docker-archive-keyring.gpg

echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update -y
sudo apt-cache policy docker-ce

# Install Docker
echo "Installing Docker..."
sudo apt-get install -y docker-ce
sudo systemctl enable --now docker

# Add user to the Docker group
echo "Adding user to Docker group..."
sudo usermod -aG docker ${tools_user}
sudo usermod -aG docker ${MSADMIN_USER}


# Install NVIDIA container toolkit
echo "Setting up NVIDIA container toolkit..."
#curl -fsSL https://nvidia.github.io/nvidia-docker/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
curl -fsSL https://nvidia.github.io/nvidia-docker/gpgkey | sudo gpg --dearmor -o /tmp/nvidia-container-toolkit-keyring.gpg
mv /tmp/nvidia-container-toolkit-keyring.gpg /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg

#echo "deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://nvidia.github.io/libnvidia-container/ubuntu22.04/$(dpkg --print-architecture) /" | sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
sudo apt-get update -y
sudo apt-get install -y nvidia-container-toolkit
sudo systemctl restart docker

# Verify installations
echo "Verifying installations..."
docker run hello-world
docker run --gpus all nvidia/cuda:11.5.2-base-ubuntu20.04 nvidia-smi

echo "Finished the Docker and NVIDIA container setup process"
