#!/bin/bash

# Define the conda environment and ComfyUI paths
CONDA_ENV="qs"
HOME_DIR="/home/<USER>"
COMFYUI_PATH="/qsfs2/services/ComfyUI/custom_nodes"

# Function to activate conda environment
activate_conda_env() {
    echo "Activating conda environment '$CONDA_ENV'"
    source "$HOME_DIR/.bashrc_conda"
    conda activate $CONDA_ENV
}

# Function to update pip packages from a requirements.txt file
update_pip_packages() {
    local req_file="$1"
    echo "Checking requirements file: $req_file"
    
    # Check if the requirements.txt file exists
    if [[ -f "$req_file" ]]; then
        echo "Installing packages from $req_file"
        pip install -r "$req_file"
    else
        echo "No requirements.txt file found at $req_file"
    fi
}

# Activate the conda environment
activate_conda_env

# Find all requirements.txt files in the ComfyUI directory and its subdirectories
echo "Scanning for requirements.txt files..."
update_pip_packages "/qsfs2/services/ComfyUI/requirements.txt"
find "$COMFYUI_PATH" -name "requirements.txt" | while read req_file; do
    update_pip_packages "$req_file"
done

# Deactivate conda environment
conda deactivate
echo "Conda environment deactivated"
