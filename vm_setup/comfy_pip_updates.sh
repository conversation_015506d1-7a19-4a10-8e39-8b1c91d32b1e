#!/bin/bash

# Define the conda environment and ComfyUI paths
CONDA_ENV="qs"
HOME_DIR="/home/<USER>"
CONDA_ENV_PATH="$HOME_DIR/.conda/envs/$CONDA_ENV"
COMFYUI_PATH="/qsfs2/services/ComfyUI/custom_nodes"
TIMESTAMP_FILE="$HOME_DIR/.last_pip_update"


# Function to activate conda environment
activate_conda_env() {
    echo "Activating conda environment '$CONDA_ENV'"
    source "$HOME_DIR/.bashrc_conda"
    conda activate $CONDA_ENV
}

# Function to update pip packages from a requirements.txt file
update_pip_packages() {
    local req_file="$1"
    echo "Checking requirements file: $req_file"
    
    # Check if the requirements.txt file exists
    if [[ -f "$req_file" ]]; then
        echo "Installing packages from $req_file"
        pip install -r "$req_file"
        touch "$TIMESTAMP_FILE"
    else
        echo "No requirements.txt file found at $req_file"
    fi
}

# Create timestamp file if it doesn't exist
if [[ ! -f "$TIMESTAMP_FILE" ]]; then
    touch "$TIMESTAMP_FILE"
fi

# Get the timestamp of the last pip package update
last_pip_update=$(stat -c %Y "$TIMESTAMP_FILE")

echo "Last pip update: $last_pip_update"

req_file_timestamp=$(stat -c %Y "/qsfs2/services/ComfyUI/requirements.txt")

# Compare the timestamp of the requirements.txt file with the last pip update
if [[ "$req_file_timestamp" -gt "$last_pip_update" ]]; then
    echo "New or updated requirements.txt found: $req_file"
    activate_conda_env
    update_pip_packages "/qsfs2/services/ComfyUI/requirements.txt"
else
    echo "No updates required for: /qsfs2/services/ComfyUI/requirements.txt"
fi

# Find all requirements.txt files in the ComfyUI directory and its subdirectories
find "$COMFYUI_PATH" -name "requirements.txt" | while read req_file; do
    req_file_timestamp=$(stat -c %Y "$req_file")

    # Compare the timestamp of the requirements.txt file with the last pip update
    if [[ "$req_file_timestamp" -gt "$last_pip_update" ]]; then
        echo "New or updated requirements.txt found: $req_file and timestamp: $req_file_timestamp"
        activate_conda_env
        update_pip_packages "$req_file"
    else
        echo ".."
    fi
done


# Deactivate conda environment
conda deactivate
echo "Conda environment deactivated"
