#!/bin/bash
set -x
# Check if machine name is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <machine-name>"
    exit 1
fi

VM_NAME=$1
DOMAIN="${VM_NAME}.onazure.quantstac.com"
EMAIL="<EMAIL>"
CERTBOT_DIR="/etc/letsencrypt/live/${DOMAIN}"
QS_USER="<EMAIL>"

 sudo DEBIAN_FRONTEND=noninteractive apt install certbot -y

# Obtain the SSL certificate
#check if the certs are already created if not then obtain
sudo certbot certonly --agree-tos --quiet --standalone -d ${DOMAIN} -m ${EMAIL}

# Check if certificate was successfully obtained
if [ ! -d "${CERTBOT_DIR}" ]; then
    echo "Failed to obtain certificate for ${DOMAIN}"
    exit 1
fi

# Ensure the $QS_USER user has read permissions on the certificate files
sudo chown -R root:$QS_USER /etc/letsencrypt/live
sudo chmod -R 750 /etc/letsencrypt/live
sudo chown -R root:$QS_USER /etc/letsencrypt/archive
sudo chmod -R 750 /etc/letsencrypt/archive
sudo chown -R root:$QS_USER /etc/letsencrypt/keys
sudo chmod -R 750 /etc/letsencrypt/keys

sudo mkdir -p /etc/letsencrypt/qstools
sudo mkdir -p /etc/letsencrypt/qstools/certs
sudo ln -s /etc/letsencrypt/live/${DOMAIN}/fullchain.pem /etc/letsencrypt/qstools/certs
sudo ln -s /etc/letsencrypt/live/${DOMAIN}/privkey.pem /etc/letsencrypt/qstools/certs
sudo ln -s /etc/letsencrypt/live/${DOMAIN}/cert.pem /etc/letsencrypt/qstools/certs
sudo ln -s /etc/letsencrypt/live/${DOMAIN}/chain.pem /etc/letsencrypt/qstools/certs

sudo chown -R root:$QS_USER /etc/letsencrypt/qstools
sudo chmod -R 750 /etc/letsencrypt/qstools

#check if certs are created for domain
if [ ! -f "/etc/letsencrypt/qstools/certs/fullchain.pem" ] || [ ! -f "/etc/letsencrypt/qstools/certs/privkey.pem" ] || [ ! -f "/etc/letsencrypt/qstools/certs/cert.pem" ] ; then
    echo "Failed to obtain certificate for ${DOMAIN}"
    echo "Failed to obtain certificate for ${DOMAIN}" >> /qsfs2/logs/${VM_NAME}/error.log
    exit 1
else
    echo "Certificate setup complete for ${DOMAIN} with permissions set for $QS_USER user."
fi
echo "Certificate setup complete for ${DOMAIN} with permissions set for $QS_USER user."

exit 0