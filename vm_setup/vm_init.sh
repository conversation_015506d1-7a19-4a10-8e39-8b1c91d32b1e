#!/bin/bash
# Ensure the script takes an argument for the first script
if [ -z "$1" ]; then
  echo "Usage: $0 <argument_for_update_hostname.sh>"
  exit 1
fi
VM_NAME="$1"

while true; do
  # Ask the user to confirm the VM_NAME
  read -r -p "You are using VM_NAME: $VM_NAME. Press [y] to confirm, [c] to change, [n] to discontinue: " choice
  
  case $choice in
    y|Y)
      echo "You have confirmed VM_NAME: $VM_NAME."
      # Proceed with the rest of the script
      break
      ;;
    c|C)
      read -r -p "Enter the new VM_NAME: " new_vm_name
      VM_NAME="$new_vm_name"
      ;;
    n|N)
      echo "Discontinued by the user."
      exit 0
      ;;
    *)
      echo "Invalid choice. Please press [y] to confirm, [c] to change, [n] to discontinue."
      ;;
  esac
done

# Rest of your script using the confirmed VM_NAME
echo "Proceeding with VM_NAME: $VM_NAME"

# Run scripts sequentially
bash ./update_hostname.sh $VM_NAME
echo "Host update complete"
sleep 1
bash ./custom_nvidia_setup.sh 
echo "Nvidia setup complete"
sleep 1
bash ./custom_setup.sh $VM_NAME
echo "Custom setup complete, updating certificates"
sleep 1
#now create certs and update them
#bash ./setup_cert.sh $VM_NAME
#run following as ms.admin user
# Reboot the machine
echo "Rebooting the machine..."
sleep 5
reboot 
disown $!
