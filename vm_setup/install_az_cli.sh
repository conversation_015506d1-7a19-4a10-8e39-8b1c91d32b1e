#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Update the package list
echo "Updating package list..."
sudo apt-get update -y

# Install prerequisites
echo "Installing prerequisites..."
sudo apt-get install -y ca-certificates curl apt-transport-https lsb-release gnupg

# Add the Microsoft GPG key
echo "Adding Microsoft GPG key..."
curl -sL https://packages.microsoft.com/keys/microsoft.asc | sudo gpg --dearmor -o /usr/share/keyrings/microsoft.gpg

# Add the Azure CLI software repository
echo "Adding Azure CLI repository..."
AZ_REPO=$(lsb_release -cs)
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/microsoft.gpg] https://packages.microsoft.com/repos/azure-cli/ $AZ_REPO main" | sudo tee /etc/apt/sources.list.d/azure-cli.list

# Update the package list again to include the Azure CLI repository
echo "Updating package list after adding Azure CLI repository..."
sudo apt-get update -y

# Install the Azure CLI
echo "Installing Azure CLI..."
sudo apt-get install -y azure-cli

# Verify the installation
echo "Verifying Azure CLI installation..."
az version

echo "Azure CLI installed successfully!"
