#!/bin/bash

# Function to check the exit code of the previous command
check_error() {
  if [ "$?" -ne 0 ]; then
    echo "Error: Previous command failed. Exiting with code 1."
    exit 1
  fi
}

# Function to check if a Public IP exists
public_ip_exists() {
  az network public-ip show --resource-group "$resourceGroup" --name "$publicIpName" --query "provisioningState" -o tsv > /dev/null 2>&1
  return $?
}

# Function to check if a Network Interface exists
nic_exists() {
  az network nic show --resource-group "$resourceGroup" --name "$networkInterfaceName" --query "provisioningState" -o tsv > /dev/null 2>&1
  return $?
}

# Function to check if a DNS CNAME record exists
cname_exists() {
  az network dns record-set cname show --resource-group "$resourceGroup" --zone-name "$qsDnsZone" --name "$VM_NAME" > /dev/null 2>&1
  return $?
}

if [ -z "$1" ]; then
  echo "Usage: $0 <vm_name>"
  exit 1
fi
VM_NAME="$1"

# Variables
resourceGroup="qsuser"
location="southcentralus"
networkInterfaceName="${VM_NAME}733_z1"
publicIpName="${VM_NAME}PublicIP"
dnsName="${VM_NAME}"
vnet="qsvnet001"
nsg="qslowgpu001-nsg"
qsDnsZone="onazure.quantstac.com"
azDnsZone="${location}.cloudapp.azure.com"

echo "Checking if Public IP '$publicIpName' exists..."
if public_ip_exists; then
  echo "Public IP '$publicIpName' already exists."
else
  echo "Creating Public IP '$publicIpName' with DNS name '$dnsName'..."
  az network public-ip create \
    --resource-group "$resourceGroup" \
    --name "$publicIpName" \
    --location "$location" \
    --allocation-method Static \
    --dns-name "$dnsName"
  check_error
  echo "Public IP '$publicIpName' created."
fi

# Get the Public IP ID (we need it even if it existed to ensure the NIC has it)
publicIpId=$(az network public-ip show --resource-group "$resourceGroup" --name "$publicIpName" --query id --output tsv)
check_error

echo "Checking if Network Interface '$networkInterfaceName' exists..."
if nic_exists; then
  echo "Network Interface '$networkInterfaceName' already exists."
else
  echo "Creating Network Interface '$networkInterfaceName'..."
  az network nic create \
    --resource-group "$resourceGroup" \
    --location "$location" \
    --name "$networkInterfaceName" \
    --vnet-name "$vnet" \
    --subnet default \
    --public-ip-address "$publicIpName" \
    --network-security-group "$nsg"
  check_error
  echo "Network Interface '$networkInterfaceName' created."
fi

echo "Updating Network Interface '$networkInterfaceName' with accelerated networking..."
az network nic update --resource-group "$resourceGroup" --name "$networkInterfaceName" --accelerated-networking true
check_error
echo "Network Interface '$networkInterfaceName' updated."

# Display the Public IP Address and DNS Name
publicIp=$(az network public-ip show --resource-group "$resourceGroup" --name "$publicIpName" --query ipAddress --output tsv)
check_error
fqdn=$(az network public-ip show --resource-group "$resourceGroup" --name "$publicIpName" --query dnsSettings.fqdn --output tsv)
check_error
echo "The public IP address of the VM is: $publicIp"
echo "The DNS name of the VM is: $fqdn"

echo "Checking if CNAME record '$VM_NAME.$qsDnsZone' exists..."
if cname_exists; then
  echo "CNAME record '$VM_NAME.$qsDnsZone' already exists."
else
  echo "Creating alias '$VM_NAME.$qsDnsZone' for '$VM_NAME.$azDnsZone'..."
  az network dns record-set cname set-record --resource-group "$resourceGroup" --zone-name "$qsDnsZone" --record-set-name "$VM_NAME" --cname "$VM_NAME.$azDnsZone" --ttl 1
  check_error
  echo "CNAME record created."
fi

echo "Network creation complete."
exit 0