#!/bin/bash

# Check if the hostname is provided as an argument
if [ -z "$1" ]; then
  echo "Usage: $0 <hostname>"
  exit 1
fi

# Set the hostname from the argument
hostname=$1

sudo hostnamectl set-hostname "$hostname"
echo "127.0.0.1 ${hostname}.msadminquantstac.onmicrosoft.com $hostname" | sudo tee -a /etc/hosts
echo "$hostname" | sudo tee /etc/hostname
echo "search msadminquantstac.onmicrosoft.com" | sudo tee -a /etc/resolv.conf

# Making resolv.conf changes permanent using netplan (if applicable)
if [ -f /etc/netplan/01-netcfg.yaml ]; then
  sudo bash -c "cat <<EOL >> /etc/netplan/01-netcfg.yaml
network:
  version: 2
  ethernets:
    eth0:
      dhcp4: true
      nameservers:
        search: [msadminquantstac.onmicrosoft.com]
EOL"
  sudo netplan apply
fi
