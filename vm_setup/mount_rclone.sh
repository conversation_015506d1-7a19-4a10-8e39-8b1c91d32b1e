#!/bin/bash
# To run this and mount team <NAME_EMAIL> files, 
# from linux machine login in <NAME_EMAIL> 
# run command rclone authorize "onedrive" 
# copy the access token in ~/.config/rclone/rclone_auth.key (which is copyied to dest on setup) file the file should have
#[msteams]
#type = onedrive
#token = {}
#drive_id = b!zY1EpDHYNEm2736fHdt0EmeX31rX-oNKoCVWeSHv6h2TBChrl1IqRq724P6wg_5N
#drive_type = documentLibrary
# replace the generated token 


echo "starting process"
cd /qsfs2/services/rclone/
#eval "$(conda shell.bash hook)" && conda activate qs 
nohup rclone --vfs-cache-mode writes mount msteams: /home/<USER>/teams_files 2>&1 > /tmp/rclone.out &
rclonepid=$!
echo "started with pid " ${rclonepid} 
sleep 10
echo "checking mount"
disown
echo ${rclonepid} > /tmp/rclone.pid
echo "running server check logs"
