#!/bin/bash
#USER=<EMAIL>
#USE_PROFILE=false
#REBOOT=false

if [ -z "$1" ]; then
    echo "Usage: $0 VM_NAME"
    exit 1
fi

VM_NAME=$1

# Paths
REPO_DIR="/home/<USER>/infra"
RCLONE_CONFIG_SRC="$REPO_DIR/rclone_auth.key"
RCLONE_CONFIG_DEST="/home/<USER>/.config/rclone/rclone.conf"
MOUNT_POINT="/home/<USER>/teams_files"

sudo apt install rclone -y 
sudo apt install fuse3 -y

# #remove current mount service
# sudo rm -f /etc/systemd/system/rclone-mount.service
# sudo systemctl daemon-reload

# User and group
USER="<EMAIL>"
GROUP="<EMAIL>"

# Ensure the destination config directory exists
sudo mkdir -p /home/<USER>/.config/rclone

# Copy rclone_auth.key to the correct directory
sudo cp $RCLONE_CONFIG_SRC $RCLONE_CONFIG_DEST

# Set ownership and permissions for rclone_auth.key conf
sudo chown -R $USER:$GROUP /home/<USER>/.config
sudo chmod 644 $RCLONE_CONFIG_DEST


# Create the mount point directory
sudo mkdir -p $MOUNT_POINT

# Change ownership and permissions of the mount point directory
sudo chown $USER:$GROUP $MOUNT_POINT
sudo chmod 777 $MOUNT_POINT


echo "Rclone service setup completed successfully. now manually mount to check"
#check if mount is successful as a qsuser and see any teams file have come



exit 0

