#!/bin/bash
#refernces used for filebrowser
#https://github.com/hurlenko/filebrowser-docker
#for openimageio conda install
#https://anaconda.org/conda-forge/py-openimageio & https://anaconda.org/conda-forge/openimageio   install openimageio then py-openimageio
# azure nfs mount doesnt work well, when apt is broken use az_nfs_install.sh  downloaded from 
# https://github.com/Azure/AZNFS-mount with wget

if [ -z "$1" ]; then
    echo "Usage: $0 <machine-name>"
    exit 1
fi

export VM_NAME=$1
#++++ Confirm new auth key +++++ is created in /home/<USER>/infra/vm_setup/rclone_auth.key "
echo "started the custom setup process for ${VM_NAME}"
echo "This script also installs rclone for QSTools"
echo "It requires latest auth key for install to work"
echo "Create an auth key with command on ubuntu wsl terminal and \
copy to /home/<USER>/infra/vm_setup/rclone_auth.key in the infra repo"

while true; do
  # Ask the user to confirm the rclone auth key
  read -r -p "Confirm: you have created new rclone auth key? Press [y] to continue, [n] to discontinue: " choice
  
  case $choice in
    y|Y)
      echo "You have confirmed you have created new rclone auth key. Continuing..."
      # Proceed with the rest of the script
      break
      ;;
    n|N)
      echo "Discontinued by the user."
      exit 0
      ;;
    *)
      echo "Invalid choice. Please press [y] to confirm or [n] to discontinue."
      ;;
  esac
done

# Function to kill hanging apt processes and remove lock files
clean_apt_locks() {
    echo "Checking and terminating any active apt processes..."
    if pgrep -x "apt" > /dev/null || pgrep -x "apt-get" > /dev/null || pgrep -x "dpkg" > /dev/null; then
        killall -9 apt apt-get dpkg || true
    fi
    echo "Removing lock files..."
    rm -f /var/lib/dpkg/lock-frontend
    rm -f /var/lib/dpkg/lock
    echo "Reconfiguring dpkg..."
    dpkg --configure -a
}

# Clean any existing apt locks and terminate hanging processes
clean_apt_locks

mkdir -p /qsfs2/logs/${VM_NAME}
# Set environment variables for non-interactive installations
#!/bin/bash
export AZNFS_NONINTERACTIVE_INSTALL=1
export DEBIAN_FRONTEND=noninteractive

# Install NFS v3 driver
echo "Installing NFS v3 driver..."
#wget -O - -q https://github.com/Azure/AZNFS-mount/releases/latest/download/aznfs_install.sh | sudo env AZNFS_NONINTERACTIVE_INSTALL=${AZNFS_NONINTERACTIVE_INSTALL} DEBIAN_FRONTEND=${DEBIAN_FRONTEND} bash
sudo bash /home/<USER>/infra/vm_setup/az_nfs_install.sh

# Create /qsfs2 directory and mount NFS
sudo mkdir -p /qsfs2
sudo chmod 777 /qsfs2
echo "qsstorage002.blob.core.windows.net:/qsstorage002/qsblobcontainer002  /qsfs2    aznfs defaults,sec=sys,vers=3,nolock,proto=tcp,nofail,_netdev    0 0" | sudo tee -a /etc/fstab
sudo mount -a

sudo mkdir -p /qsfs2/logs/${VM_NAME}
sudo chmod 777 /qsfs2/logs/${VM_NAME}
touch /qsfs2/logs/${VM_NAME}/error.log
#check if the qsfs2 mount has worked by checking if /qsfs2/services directory exists
if [ ! -d "/qsfs2/services" ]; then
    echo "Failed to mount NFS share"
    echo "Failed to mount NFS share" >> /qsfs2/logs/${VM_NAME}/error.log
fi


# Clean apt locks before Miniconda installation
clean_apt_locks


# Install Miniconda
echo "Installing Miniconda..."
sudo mkdir -p /opt/miniconda3
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /opt/miniconda3/miniconda.sh
sudo bash /opt/miniconda3/miniconda.sh -b -u -p /opt/miniconda3
sudo rm -rf /opt/miniconda3/miniconda.sh

#check if conda install worked
if [ ! -f "/opt/miniconda3/bin/conda" ]; then
    echo "Failed to install Miniconda"
    echo "Failed to install Miniconda" >> /qsfs2/logs/${VM_NAME}/error.log
fi


#install tkinter
DEBIAN_FRONTEND=noninteractive apt install python3-tk -y
DEBIAN_FRONTEND=noninteractive  apt install zip -y
DEBIAN_FRONTEND=noninteractive  apt install certbot -y


echo "Installing GitHub CLI..."
(type -p wget >/dev/null || (DEBIAN_FRONTEND=noninteractive   apt update && DEBIAN_FRONTEND=noninteractive  apt-get install wget -y)) \
&& sudo mkdir -p -m 755 /etc/apt/keyrings \
&& wget -qO- https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo tee /etc/apt/keyrings/githubcli-archive-keyring.gpg > /dev/null \
&& sudo chmod go+r /etc/apt/keyrings/githubcli-archive-keyring.gpg \
&& echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
&& DEBIAN_FRONTEND=noninteractive   sudo apt update -y \
&& DEBIAN_FRONTEND=noninteractive   sudo apt install gh -y

echo "Installing bindfs"
DEBIAN_FRONTEND=noninteractive sudo apt install bindfs -y

# Upgrade all packages non-interactively
echo "Upgrading packages..."
DEBIAN_FRONTEND=noninteractive sudo apt upgrade -y

# Upgrade all packages again to ensure everything is up to date
echo "Performing full upgrade..."
DEBIAN_FRONTEND=noninteractive sudo apt full-upgrade -y

clean_apt_locks

# Function to add a user to a group
add_user_to_group() {
    local user=$1
    local group=$2
    if id -u $user >/dev/null 2>&1; then
        sudo usermod -a -G $group $user
        echo "User '$user' added to group '$group'."
    else
        echo "User '$user' does not exist. failed adding"
        echo "User '$user' does not exist. failed adding" >> /qsfs2/logs/${VM_NAME}/error.log
    fi
}

# Variables
MSADMIN_USER="<EMAIL>"
MSADMIN_USER_ID="5fc832b8-5c5e-4625-b28d-2c0a68ba7137"
GROUP_ADMIN="admin"
GROUP_AAD_ADMINS="aad_admins"
GROUP_ADMIN_ID="118"
GROUP_AAD_ADMINS_ID="1001"
AADPASSWD_FILE="/etc/aadpasswd"
QS_USER_ID="e06b8ae2-cab3-48c8-aa2e-fa088d4eaa2a"
QS_USER="<EMAIL>"
#adminUsername="qsuser"
acrAppId="************************************"
acrPwd="****************************************"
acrTenant="73b55c4d-0679-4a00-acbf-3ef8759d1c31"


# Check if group 'admin' exists, if not create it with the specified GID
if ! getent group ${GROUP_ADMIN} >/dev/null; then
    sudo groupadd -g ${GROUP_ADMIN_ID} ${GROUP_ADMIN}
    echo "Group '${GROUP_ADMIN}' created with GID ${GROUP_ADMIN_ID}."
fi

# Check if group 'aad_admins' exists, if not create it with the specified GID
if ! getent group ${GROUP_AAD_ADMINS} >/dev/null; then
    sudo groupadd -g ${GROUP_AAD_ADMINS_ID} ${GROUP_AAD_ADMINS}
    echo "Group '${GROUP_AAD_ADMINS}' created with GID ${GROUP_AAD_ADMINS_ID}."
fi

# Check if the user exists in /etc/aadpasswd
if ! grep -q "^${MSADMIN_USER}:" ${AADPASSWD_FILE}; then
    sudo aaduseradd -m -s /bin/bash -o ${MSADMIN_USER_ID} ${MSADMIN_USER}
    echo "AAD user '${MSADMIN_USER}' created."
else
    echo "AAD user '${MSADMIN_USER}' already exists."
fi

# Add the AAD user to the specified groups
add_user_to_group ${MSADMIN_USER} ${GROUP_ADMIN}
add_user_to_group ${MSADMIN_USER} ${GROUP_AAD_ADMINS}

# Verify if the user is in the groups
if groups ${MSADMIN_USER} | grep &>/dev/null "\b${GROUP_ADMIN}\b" && groups ${MSADMIN_USER} | grep &>/dev/null "\b${GROUP_AAD_ADMINS}\b"; then
    echo "User '${MSADMIN_USER}' is correctly assigned to groups '${GROUP_ADMIN}' and '${GROUP_AAD_ADMINS}'."
else
    echo "There was an issue adding the user '${MSADMIN_USER}' to the groups."
fi

# Allow no password sudo for sudo group
if sudo grep -q '^%sudo' /etc/sudoers; then
  sudo sed -i 's/^%sudo.*/%sudo ALL=(ALL:ALL) NOPASSWD:ALL/' /etc/sudoers
else
  echo '%sudo ALL=(ALL:ALL) NOPASSWD:ALL' | sudo tee -a /etc/sudoers
fi

# Now create qstools user
sudo aaduseradd -m -s /bin/bash -o ${QS_USER_ID} ${QS_USER}

sudo chown ${QS_USER} /qsfs2/logs/${VM_NAME}/error.log

if [ $? -eq 0 ]; then
    echo "User '<EMAIL>' added  successfully."
else
    echo "Failed to add user '<EMAIL>'."
    echo "Manually add user '<EMAIL>'" >> /qsfs2/logs/${VM_NAME}/error.log
fi

echo "AAD user '${QS_USER}' created."


#add the admin user to the sudo group
sudo usermod -aG sudo ${MSADMIN_USER} 
#sudo usermod -aG sudo $adminUsername

#add qsuser to sudo group
sudo usermod -aG sudo ${QS_USER}

if [ $? -eq 0 ]; then
    echo "User '<EMAIL>' added to sudo group successfully."
else
    echo "Failed to add user '<EMAIL>' to sudo group."
    echo "Manually add user '<EMAIL>' to sudo group." >> /qsfs2/logs/${VM_NAME}/error.log
fi
cp ./profile_d_conda.sh /opt/miniconda/etc/profile.d/conda.sh

# Update .bashrc for all users to enable Miniconda
for user in ${MSADMIN_USER} ${QS_USER}; do
  sudo -u $user bash -c 'cat <<EOL >> ~/.bashrc
# >>> conda initialize >>>
__conda_setup="\$("/opt/miniconda3/bin/conda" "shell.bash" "hook" 2> /dev/null)"
if [ \$? -eq 0 ]; then
    eval "\$__conda_setup"
else
    if [ -f "/opt/miniconda3/etc/profile.d/conda.sh" ]; then
        . "/opt/miniconda3/etc/profile.d/conda.sh"
    else
        export PATH="/opt/miniconda3/bin:\$PATH"
    fi
fi
unset __conda_setup
# <<< conda initialize <<<
EOL

  cat <<EOL > ~/.bashrc_conda
# >>> conda initialize >>>
__conda_setup="\$("/opt/miniconda3/bin/conda" "shell.bash" "hook" 2> /dev/null)"
if [ \$? -eq 0 ]; then
    eval "\$__conda_setup"
else
    if [ -f "/opt/miniconda3/etc/profile.d/conda.sh" ]; then
        . "/opt/miniconda3/etc/profile.d/conda.sh"
    else
        export PATH="/opt/miniconda3/bin:\$PATH"
    fi
fi
unset __conda_setup
# <<< conda initialize <<<
#conda activate qs
EOL
'
done



#now setup certificates
sudo  bash ./setup_cert.sh ${VM_NAME}

if [ $? -eq 0 ]; then
    echo "Certificates installed successfully."
else
    echo "Failed to install certificates."
    echo "Manually install certificates." >> /qsfs2/logs/${VM_NAME}/error.log
fi
#now create conda env for QSUser

#!/bin/bash

# Extract the last three digits of the machine name
machine_number=${VM_NAME: -3}
# Calculate the minute offset (0-59)
machine_id=$((10#${machine_number} % 2))
minute_offset=$(($((10#${machine_number} % 10))*$((5+$machine_id))))
dd=`date`

sudo cp -R /home/<USER>/infra/docker_scripts /tmp 
chown -R ${QS_USER} /tmp/docker_scripts

cp -f -R /home/<USER>/infra/jupyter /tmp
chown -R ${QS_USER} /tmp/jupyter
#now create conda env for qsuser
# new process
cp ./qstools_cronjobs.sh /tmp
cp ./msadmin_cronjobs.sh /tmp
chmod 777 /tmp/qstools_cronjobs.sh
chmod 777 /tmp/msadmin_cronjobs.sh

sudo -H -i -u ${QS_USER} bash << EOF

echo "Creating directories for proxy and filebrowser"
#now (enable ) nginx, oauth2_proxy and filebrowser 
# mkdir -p /qsfs2/qsvar/${VM_NAME}/proxy/nginx
# mkdir -p /qsfs2/qsvar/${VM_NAME}/proxy/nginx/certs
# mkdir -p /qsfs2/qsvar/${VM_NAME}/proxy/oauth2_proxy
# mkdir -p /qsfs2/qsvar/${VM_NAME}/filebrowser
# mkdir -p /qsfs2/qsvar/${VM_NAME}/filebrowser/config
# mkdir -p /qsfs2/qsvar/${VM_NAME}/qsComfy
if [ $? -eq 0 ]; then
    echo "Directories created successfully."
else
    echo "Failed to create directories."    
    echo "Manually create directories." >> /qsfs2/logs/${VM_NAME}/error.log 
fi
echo "Copying proxy and filebrowser docker-compose files"

#copy proxy nginx.conf docker-compose, filebrowser docker-compose etc. and replace qst4spot010 with ${VM_NAME}

# cp -f /tmp/docker_scripts/proxy/nginx/nginx.conf /qsfs2/qsvar/${VM_NAME}/proxy/nginx/nginx.conf
# sed -i "s/qst4spot010/${VM_NAME}/g" /qsfs2/qsvar/${VM_NAME}/proxy/nginx/nginx.conf

# cp -f /tmp/docker_scripts/proxy/docker-compose.yml /qsfs2/qsvar/${VM_NAME}/proxy/docker-compose.yml
# sed -i "s/qst4spot010/${VM_NAME}/g" /qsfs2/qsvar/${VM_NAME}/proxy/docker-compose.yml

# cp -f /tmp/docker_scripts/filebrowser/docker-compose.yml /qsfs2/qsvar/${VM_NAME}/filebrowser/docker-compose.yml
# sed -i "s/qst4spot010/${VM_NAME}/g" /qsfs2/qsvar/${VM_NAME}/filebrowser/docker-compose.yml

echo "Creating cron jobs"

crontab /tmp/qstools_cronjobs.sh

echo "Creating conda environments"

source /opt/miniconda3/etc/profile.d/conda.sh
#if no conda found exit
which conda 
if [ $? -ne 0 ]; then
    echo "conda not found"
    exit 1
fi
source ~/.bashrc  # Ensure .bashrc is sourced after conda create

echo "installing jupyter conda env"

conda create --name jupyter python=3.12.2 -y && eval "$(conda shell.bash hook)" && unset PYTHONPATH && conda activate jupyter

pip install torch==2.5.1+cu124 torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124
pip install torch==2.5.1+cu124 -U  xformers --index-url https://download.pytorch.org/whl/cu124
pip install -r /tmp/jupyter/frozen_jupyter_requirements.txt
conda deactivate ; conda deactivate

git config --global user.email "<EMAIL>"
git config --global user.name "suresh.mali"

#installing conda from another script 
bash /qsfs2/services/qsComfy/qs_reqs/recreate_envs.sh

if [ $? -eq 0 ]; then
    echo "conda environment created successfully."
else
    echo "Failed to create conda environment."
    echo "Manually create conda environment." >> /qsfs2/logs/${VM_NAME}/error.log
fi

EOF


#rclone must be installed after conda is etup
#install rclone with setup_rclone.sh script
bash ./setup_rclone.sh ${VM_NAME}
#check if rclone is installed 
if [ $? -eq 0 ]; then
    echo "rclone installed successfully."
else
    echo "Failed to install rclone."
    echo "Manually install rclone." >> /qsfs2/logs/${VM_NAME}/error.log
fi

#create a cron entry for ms.admin user
sudo -H -i -u ${MSADMIN_USER} bash << EOF

crontab /tmp/msadmin_cronjobs.sh

EOF


# Clean any apt locks and terminate hanging processes again at the end
clean_apt_locks

echo "Installing azure cli"
bash ./install_az_cli.sh
echo "azure cli installed"
sleep 2

clean_apt_locks


clear_apt_locks

echo "Setting up docker"
bash ./docker_setup.sh $VM_NAME
echo "Docker setup complete"

clean_apt_locks

sudo -H -i -u ${QS_USER} bash << EOF

echo "Pulling qsComfy docker image "
export acrAppId="************************************"
export acrPwd="****************************************"
export acrTenant="73b55c4d-0679-4a00-acbf-3ef8759d1c31"

az login --service-principal --username $acrAppId  --password $acrPwd --tenant $acrTenant && sleep 2 && az acr login --name qsacr001 && docker pull qsacr001.azurecr.io/qs-comfy:latest

docker images | grep qs-comfy
  
EOF

clean_apt_locks


echo "Following were the error correct manually"
cat /qsfs2/logs/${VM_NAME}/error.log
#wait for user input for reboot
read -n1 -r -p "Press any key to continue..." key

echo "+++All setup commands ran successfully. Reboot the machine and start using it+++"
