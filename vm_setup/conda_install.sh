#!/bin/bash

# Variables
CONDA_DIR="/opt/miniconda"
CONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-py310_23.3.1-0-Linux-x86_64.sh"
SHARED_ENVS_DIR="/qsfs2/conda_commons/envs"
SHARED_PKGS_DIR="/qsfs2/conda_commons/pkgs"
CONDA_PROFILE="/etc/profile.d/conda.sh"
CONDA_RC="/etc/conda/condarc"

# Step 1: Download and Install Miniconda
echo "Downloading Miniconda..."
wget -q $CONDA_URL -O /tmp/Miniconda3-latest-Linux-x86_64.sh

echo "Installing Miniconda..."
bash /tmp/Miniconda3-latest-Linux-x86_64.sh -b -p $CONDA_DIR

# Step 2: Configure Global Conda Settings
echo "Configuring global Conda settings..."

# Create conda.sh in /etc/profile.d/ to make Conda available to all users
echo "export PATH=\"$CONDA_DIR/bin:\$PATH\"" | sudo tee $CONDA_PROFILE > /dev/null
echo "export CONDA_ENVS_PATH=\"$SHARED_ENVS_DIR\"" | sudo tee -a $CONDA_PROFILE > /dev/null
echo "export CONDA_PKGS_DIRS=\"$SHARED_PKGS_DIR\"" | sudo tee -a $CONDA_PROFILE > /dev/null

sudo chmod +x $CONDA_PROFILE

# Create global condarc file to set shared directories
sudo mkdir -p /etc/conda
sudo tee $CONDA_RC > /dev/null <<EOL
envs_dirs:
  - $SHARED_ENVS_DIR
pkgs_dirs:
  - $SHARED_PKGS_DIR
EOL

# Step 3: Initialize Conda
echo "Initializing Conda..."
sudo $CONDA_DIR/bin/conda init

# Step 4: Install Python 3.10 in the base environment
echo "Installing Python 3.10 in the base environment..."
sudo $CONDA_DIR/bin/conda install -y python=3.10

# Disable auto-activation of the base environment
echo "Disabling auto-activation of the base environment..."
#$CONDA_DIR/bin/conda config --system --set auto_activate_base false

rm /tmp/Miniconda3-latest-Linux-x86_64.sh

echo "Miniconda installation and configuration complete."
