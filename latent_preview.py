import torch
from PIL import Image, ImageDraw, ImageFont
import struct
import numpy as np
from comfy.cli_args import args, LatentPreviewMethod
from comfy.taesd.taesd import TAESD
import comfy.model_management
import folder_paths
import comfy.utils
import logging

MAX_PREVIEW_RESOLUTION = args.preview_size

def preview_to_image(latent_image):
        latents_ubyte = (((latent_image + 1.0) / 2.0).clamp(0, 1)  # change scale from -1..1 to 0..1
                            .mul(0xFF)  # to 0..255
                            )
        if comfy.model_management.directml_enabled:
                latents_ubyte = latents_ubyte.to(dtype=torch.uint8)
        latents_ubyte = latents_ubyte.to(device="cpu", dtype=torch.uint8, non_blocking=comfy.model_management.device_supports_non_blocking(latent_image.device))

        return Image.fromarray(latents_ubyte.numpy())

class LatentPreviewer:
    def decode_latent_to_preview(self, x0):
        pass

    def decode_latent_to_preview_image(self, preview_format, x0):
        preview_image = self.decode_latent_to_preview(x0)
        return ("JPEG", preview_image, MAX_PREVIEW_RESOLUTION)

class TAESDPreviewerImpl(LatentPreviewer):
    def __init__(self, taesd):
        self.taesd = taesd

    def decode_latent_to_preview(self, x0):
        x_sample = self.taesd.decode(x0[:1])[0].movedim(0, 2)
        return preview_to_image(x_sample)


class Latent2RGBPreviewer(LatentPreviewer):
    def __init__(self, latent_rgb_factors, latent_rgb_factors_bias=None):
        self.latent_rgb_factors = torch.tensor(latent_rgb_factors, device="cpu").transpose(0, 1)
        self.latent_rgb_factors_bias = None
        if latent_rgb_factors_bias is not None:
            self.latent_rgb_factors_bias = torch.tensor(latent_rgb_factors_bias, device="cpu")

    def decode_latent_to_preview(self, x0):
        self.latent_rgb_factors = self.latent_rgb_factors.to(dtype=x0.dtype, device=x0.device)
        if self.latent_rgb_factors_bias is not None:
            self.latent_rgb_factors_bias = self.latent_rgb_factors_bias.to(dtype=x0.dtype, device=x0.device)

        if x0.ndim == 5:
            x0 = x0[0, :, 0]
        else:
            x0 = x0[0]

        latent_image = torch.nn.functional.linear(x0.movedim(0, -1), self.latent_rgb_factors, bias=self.latent_rgb_factors_bias)
        # latent_image = x0[0].permute(1, 2, 0) @ self.latent_rgb_factors

        return preview_to_image(latent_image)


def get_previewer(device, latent_format):
    previewer = None
    method = args.preview_method
    if method != LatentPreviewMethod.NoPreviews:
        # TODO previewer methods
        taesd_decoder_path = None
        if latent_format.taesd_decoder_name is not None:
            taesd_decoder_path = next(
                (fn for fn in folder_paths.get_filename_list("vae_approx")
                    if fn.startswith(latent_format.taesd_decoder_name)),
                ""
            )
            taesd_decoder_path = folder_paths.get_full_path("vae_approx", taesd_decoder_path)

        if method == LatentPreviewMethod.Auto:
            method = LatentPreviewMethod.Latent2RGB

        if method == LatentPreviewMethod.TAESD:
            if taesd_decoder_path:
                taesd = TAESD(None, taesd_decoder_path, latent_channels=latent_format.latent_channels).to(device)
                previewer = TAESDPreviewerImpl(taesd)
            else:
                logging.warning("Warning: TAESD previews enabled, but could not find models/vae_approx/{}".format(latent_format.taesd_decoder_name))

        if previewer is None:
            if latent_format.latent_rgb_factors is not None:
                previewer = Latent2RGBPreviewer(latent_format.latent_rgb_factors, latent_format.latent_rgb_factors_bias)
    return previewer

def add_text_to_image_top(latent_image, text):
    # Convert the latent image tensor (C, H, W) to a PIL image (H, W, C)
    latent_np = latent_image.permute(1, 2, 0).cpu().numpy() * 255  # Convert back to [0, 255] range
    latent_np = latent_np.astype(np.uint8)
    pil_image = Image.fromarray(latent_np)

    # Load the default font
    font = ImageFont.load_default()

    # Create an ImageDraw object
    draw = ImageDraw.Draw(pil_image)

    # Get the bounding box for the text using textbbox
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]

    # Add text to the top of the image, centered
    image_width = pil_image.width
    text_position = ((image_width - text_width) // 2, 5)  # Adjust the position as needed
    draw.text(text_position, text, font=font, fill="black")

    # Convert the PIL image back to a PyTorch tensor
    text_image_np = np.array(pil_image).transpose(2, 0, 1)  # Convert back to (C, H, W)
    text_image_tensor = torch.from_numpy(text_image_np).float() / 255.0  # Normalize to [0, 1]

    return text_image_tensor

def prepare_callback(model, steps, x0_output_dict=None):
    preview_format = "JPEG"
    if preview_format not in ["JPEG", "PNG"]:
        preview_format = "JPEG"

    previewer = get_previewer(model.load_device, model.model.latent_format)

    pbar = comfy.utils.ProgressBar(steps)
    def callback(step, x0, x, total_steps):
        if x0_output_dict is not None:
            x0_output_dict["x0"] = x0

        preview_bytes = None
        if previewer:
            preview_bytes = previewer.decode_latent_to_preview_image(preview_format, x0)
        pbar.update_absolute(step + 1, total_steps, preview_bytes)
    return callback

def get_preview_callback(device, latent_fmt, total_steps,x_desc,show_idx,total_steps_to_show=None,vae_decoder=None):
    from comfy.latent_formats import SD15, SD3,SDXL, Flux, SDXL_Playground_2_5
    switcher={
        'SD15': SD15,
        'SD3': SD3,
        'SDXL': SDXL,
        'Flux': Flux,
        'SDXL_Playground_2_5': SDXL_Playground_2_5
    }
    ss = switcher.get(latent_fmt, SDXL)
    latent_format = ss()
    x_desc = x_desc
    show_idx = show_idx
    vae_decoder = vae_decoder
    total_steps_to_show = total_steps_to_show
 #   previewer = get_previewer(device, latent_format)
    pbar = comfy.utils.ProgressBar(total_steps)
    preview_format = "JPEG"
    def callback(x,step, nsteps=None,n_x_desc=None,n_show_idx=None,n_total_steps_to_show=None,n_vae_decoder=None):
        if n_show_idx is None :
            n_show_idx = show_idx
        if n_show_idx is None or len(n_show_idx) == 0:
            pbar.update_absolute(step + 1, nsteps) # no rendering of preview
            return
        if n_x_desc is None:
            n_x_desc = x_desc
        if nsteps is None:
            nsteps = total_steps
        if n_vae_decoder is None:
            n_vae_decoder = vae_decoder
        latents = None
        latent_rgb_factors = None
        if n_total_steps_to_show is None:
            n_total_steps_to_show = total_steps_to_show
        if n_total_steps_to_show is None:
            n_total_steps_to_show = nsteps
        # step_show_factor = nsteps / n_total_steps_to_show
        # if step % step_show_factor != 0 and step != 0 and step != nsteps - 1:
        #     # in between step update progress and return
        #     pbar.update_absolute(step + 1, nsteps)
        #     return    
        if n_vae_decoder is not None:
            latents = n_vae_decoder(x,n_show_idx)
        else :
            latent_rgb_factors = torch.tensor(latent_format.latent_rgb_factors, device="cpu")
            latent_rgb_factors = latent_rgb_factors.to(dtype=x.dtype, device=x.device)
            latents= x
        selected_dims = [n_x_desc['t'], n_x_desc['c'], n_x_desc['h'], n_x_desc['w']]
        
        # We need to reduce the tensor to only the relevant dimensions
        # Let's use `torch.index_select` to create a new tensor
        latents = latents.permute(selected_dims + [dim for dim in range(x.dim()) if dim not in selected_dims])
         # Slice the tensor to drop irrelevant dimensions, keeping only the first three
        for nx in range (len(latents.shape)-4):
            latents = latents[:,:,:,:,0]
        separator_thickness = 5
        device=latents.device
        # Create horizontal and vertical separator lines
        h_separator = torch.ones(latents[0].shape[0], separator_thickness, latents[0].shape[2]).to(device)
        v_separator = torch.ones(latents[0].shape[0],latents[0].shape[1], separator_thickness).to(device)

        # if there are more than 1 frames to show loop concat in tile manner 2 x 2
        show_latents = latents[0]
        match len(n_show_idx):
            case 2: # concat along vertical direction 1 below other
                show_latents = torch.cat((latents[show_idx[0]],h_separator, latents[show_idx[1]]), 1)
            case 3:  # concat 0, 1 horizontal and 2 below
                t1=torch.cat((latents[show_idx[0]],v_separator, latents[show_idx[1]]), 2)
                t2=torch.cat((latents[show_idx[2]],v_separator, torch.zeros_like(latents[show_idx[2]]) ), 2)
                h_separator = torch.ones(t1.shape[0], separator_thickness, t1.shape[2])
                show_latents = torch.cat((t1,h_separator, t2), 1)
            case 4: # concat 0, 1,  horizontal and 2,3 below
                t1=torch.cat((latents[show_idx[0]],v_separator, latents[show_idx[1]]), 2)
                t2=torch.cat((latents[show_idx[2]],v_separator, latents[show_idx[3]]), 2)
                h_separator = torch.ones(t1.shape[0], separator_thickness, t1.shape[2])
                show_latents = torch.cat((t1,h_separator, t2), 1)
            case _:
                pass 
        text= f'frames: {show_idx} step: {step}'
        show_latents=add_text_to_image_top(show_latents, text)         
        # Print the shape of the resulting latents tensor
        max_size=max(show_latents.shape[-1],show_latents.shape[-2])
        print(show_latents.shape)
        latent_image = show_latents.permute(1, 2, 0) 
        if latent_rgb_factors is not None: 
            latent_image = latent_image @ latent_rgb_factors
        preview_bytes = preview_to_image(latent_image)
        pbar.update_absolute(step + 1, nsteps, ['JPEG',preview_bytes,max_size])
      
    return callback
    