#!/bin/bash
#default disabled_nodes = disabled_nodes.txt if $1 assign to disabled_nodes
#default PORT = 18892, if $2 assign to port 
echo "Usage start.sh port [conda_env=qs]"

PORT=$1
if [ -z "$2" ]
then
    conda_env=qs
else
    conda_env=$2
fi

echo "++ starting comfyui in with port ${PORT}, run_type ${run_type} +++ ${conda_env}"

existing=$(ps -aef | grep python | grep main.py | grep $PORT)
echo ${existing}
if [[ -z "${existing}" ]]; then

    attempt=1
    max_attempts=6
    eval "$(conda shell.bash hook)" && conda deactivate && unset PYTHONPATH && conda activate $conda_env
    conda env list
    
    sleep 2
    while [ $attempt -le $max_attempts ]; do
        echo "Attempt $attempt to start process"
        echo "echo $CONDA_DEFAULT_ENV"
        nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
               --use-pytorch-cross-attention --port $PORT 2>&1 >/tmp/comfy_$PORT.out &

        comfyuipid=$!
        echo "Started with pid ${comfyuipid}"
        disown
        echo ${comfyuipid} > /tmp/comfy_${PORT}.pid

        # Wait for a moment and check if the process is still running
        sleep 15
        if ps -p $comfyuipid > /dev/null; then
            echo "Process started successfully"
            echo "Logs are here: /tmp/comfy_$PORT.out"
            exit 0
        else
            echo "Failed to start process. Retrying in 15 seconds..."
            attempt=$((attempt + 1))
        fi
    done

    echo "Failed to start the process after $max_attempts attempts"
    exit 1
else
    echo "Process already running. Stop it first."
    exit 1
fi
