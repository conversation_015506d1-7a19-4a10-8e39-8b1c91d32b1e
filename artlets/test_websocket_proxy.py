#!/usr/bin/env python3
"""
Test script for Artlet websocket proxy functionality
"""

import json
import logging
import time

# Test just the message transformer without websocket dependencies
try:
    from artlets.artlet_websocket_proxy import ArtletMessageTransformer
    WEBSOCKET_AVAILABLE = True
except ImportError as e:
    print(f"Websocket dependencies not available: {e}")
    WEBSOCKET_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.DEBUG)

def test_message_transformer():
    """Test the message transformer functionality"""
    if not WEBSOCKET_AVAILABLE:
        print("Skipping message transformer test - websocket dependencies not available")
        return

    print("Testing ArtletMessageTransformer...")

    # Create transformer
    transformer = ArtletMessageTransformer(
        local_prompt_id="local_123",
        remote_prompt_id="remote_456", 
        local_client_id="client_abc",
        remote_client_id="client_xyz",
        node_id_mapping={
            "remote_node_1": "local_node_A",
            "remote_node_2": "local_node_B",
            "remote_node_3": "local_node_C"
        }
    )
    
    # Test cases
    test_cases = [
        # Test executing message
        {
            "event": "executing",
            "data": {
                "node": "remote_node_1",
                "display_node": "remote_node_1", 
                "prompt_id": "remote_456"
            },
            "expected": {
                "node": "local_node_A",
                "display_node": "local_node_A",
                "prompt_id": "local_123"
            }
        },
        # Test progress message
        {
            "event": "progress",
            "data": {
                "value": 5,
                "max": 10,
                "prompt_id": "remote_456",
                "node": "remote_node_2"
            },
            "expected": {
                "value": 5,
                "max": 10,
                "prompt_id": "local_123",
                "node": "local_node_B"
            }
        },
        # Test execution_cached message
        {
            "event": "execution_cached",
            "data": {
                "nodes": ["remote_node_1", "remote_node_3"],
                "prompt_id": "remote_456"
            },
            "expected": {
                "nodes": ["local_node_A", "local_node_C"],
                "prompt_id": "local_123"
            }
        },
        # Test execution_error message
        {
            "event": "execution_error",
            "data": {
                "prompt_id": "remote_456",
                "node_id": "remote_node_2",
                "executed": ["remote_node_1"],
                "exception_message": "Test error"
            },
            "expected": {
                "prompt_id": "local_123",
                "node_id": "local_node_B",
                "executed": ["local_node_A"],
                "exception_message": "Test error"
            }
        }
    ]
    
    # Run tests
    for i, test_case in enumerate(test_cases):
        print(f"\nTest case {i+1}: {test_case['event']}")
        
        event, transformed_data = transformer.transform_message(
            test_case["event"], 
            test_case["data"]
        )
        
        print(f"  Original: {test_case['data']}")
        print(f"  Transformed: {transformed_data}")
        print(f"  Expected: {test_case['expected']}")
        
        # Check if transformation is correct
        success = True
        for key, expected_value in test_case["expected"].items():
            if transformed_data.get(key) != expected_value:
                print(f"  ❌ FAIL: {key} = {transformed_data.get(key)}, expected {expected_value}")
                success = False
        
        if success:
            print(f"  ✅ PASS")
        else:
            print(f"  ❌ FAIL")
    
    print("\nMessage transformer tests completed.")


def mock_send_proxy_func(event, data, client_id):
    """Mock function to simulate sending messages to local frontend"""
    print(f"MOCK SEND: event={event}, client_id={client_id}, data={data}")


def test_websocket_proxy_creation():
    """Test creating a websocket proxy (without actual websocket)"""
    if not WEBSOCKET_AVAILABLE:
        print("Skipping websocket proxy test - websocket dependencies not available")
        return

    print("\nTesting ArtletWebsocketProxy creation...")

    try:
        from artlets.artlet_websocket_proxy import create_artlet_websocket_proxy
        
        # Create a mock websocket object
        class MockWebSocket:
            def close(self):
                print("Mock websocket closed")
            
            def settimeout(self, timeout):
                pass
                
            def recv(self):
                # Simulate a websocket message
                return json.dumps({
                    "type": "executing",
                    "data": {
                        "node": "remote_node_1",
                        "prompt_id": "remote_456"
                    }
                })
        
        mock_ws = MockWebSocket()
        
        # Create proxy
        proxy = create_artlet_websocket_proxy(
            server_instance=None,  # Mock
            local_client_id="client_abc",
            local_prompt_id="local_123",
            remote_websocket=mock_ws,
            remote_prompt_id="remote_456",
            remote_client_id="client_xyz",
            node_id_mapping={"remote_node_1": "local_node_A"},
            send_proxy_func=mock_send_proxy_func
        )
        
        print("✅ Websocket proxy created successfully")
        
        # Test stopping the proxy
        proxy.stop_proxying()
        print("✅ Websocket proxy stopped successfully")
        
    except Exception as e:
        print(f"❌ Error creating websocket proxy: {e}")


if __name__ == "__main__":
    print("=== Artlet Websocket Proxy Tests ===")
    
    test_message_transformer()
    test_websocket_proxy_creation()
    
    print("\n=== Tests completed ===")
