#!/usr/bin/env python3
"""
Test script for Artlet deadlock detection functionality.

This script demonstrates how the deadlock detection system works by simulating
various scenarios where ArtletClient and ArtletServer might get stuck waiting
for each other.
"""

import time
import json
import urllib.request
import logging

# Define Status enum locally for testing
class Status:
    PENDING = "pending"
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_health_check_route(server_address, prompt_id):
    """Test the health check route"""
    try:
        url = f"{server_address}/artlet/health/{prompt_id}"
        req = urllib.request.Request(url, method="GET")
        response = urllib.request.urlopen(req, timeout=5)
        
        if response.status == 200:
            result = response.read()
            health_data = json.loads(result.decode('utf-8'))
            print(f"Health check successful: {health_data}")
            return health_data
        else:
            print(f"Health check failed with status: {response.status}")
            return None
            
    except Exception as e:
        print(f"Health check failed: {e}")
        return None

def test_alive_signal(server_address, prompt_id, waiting_for_server=False):
    """Test sending alive signal to server"""
    try:
        url = f"{server_address}/artlet/health/{prompt_id}/alive"
        
        alive_data = {
            "status": "alive",
            "waiting_for_server": waiting_for_server,
            "timestamp": time.time()
        }
        
        data = json.dumps(alive_data).encode('utf-8')
        headers = {"Content-Type": "application/json"}
        
        req = urllib.request.Request(url, data=data, headers=headers, method="POST")
        response = urllib.request.urlopen(req, timeout=5)
        
        if response.status == 200:
            print(f"Alive signal sent successfully: waiting_for_server={waiting_for_server}")
            return True
        else:
            print(f"Failed to send alive signal, status: {response.status}")
            return False
            
    except Exception as e:
        print(f"Failed to send alive signal: {e}")
        return False

def simulate_deadlock_scenario_1():
    """
    Scenario 1: Server waiting for inputs, client appears dead
    This simulates when a client crashes while server is waiting for inputs
    """
    print("\n=== Scenario 1: Client appears dead while server waits ===")
    
    # This would be detected by the server-side health check in ArtletServerInputBlock
    # when client hasn't sent alive signal for > 30 seconds
    
    print("1. Server starts waiting for inputs from client")
    print("2. Client sends alive signals normally for a while")
    print("3. Client stops sending alive signals (simulating crash)")
    print("4. After 30 seconds, server detects deadlock and throws exception")
    print("5. Server cleans up and abandons execution")
    
    # In real implementation, this would be handled by the health check in the wait loop

def simulate_deadlock_scenario_2():
    """
    Scenario 2: Client waiting for outputs, server waiting for different inputs
    This simulates a circular dependency or misconfiguration
    """
    print("\n=== Scenario 2: Circular dependency deadlock ===")
    
    print("1. Client waits for outputs from server block A")
    print("2. Server block A waits for inputs from client block B")
    print("3. Client block B waits for outputs from server block A")
    print("4. Deadlock detected: both sides waiting for each other")
    print("5. Exception thrown with detailed information about the deadlock")

def simulate_deadlock_scenario_3():
    """
    Scenario 3: Server appears dead while client waits
    This simulates when server crashes or becomes unresponsive
    """
    print("\n=== Scenario 3: Server appears dead while client waits ===")
    
    print("1. Client waits for outputs from server")
    print("2. Client sends periodic alive signals to server")
    print("3. Client checks server health periodically")
    print("4. Server stops responding to health checks")
    print("5. After 30 seconds, client detects server is dead")
    print("6. Client throws exception and cleans up")

def demonstrate_health_monitoring():
    """
    Demonstrate the health monitoring system
    """
    print("\n=== Health Monitoring System ===")
    
    print("Health Check Routes:")
    print("- GET /artlet/health/{prompt_id} - Get server execution status")
    print("- POST /artlet/health/{prompt_id}/alive - Send client alive signal")
    
    print("\nHealth Check Response Format:")
    health_response = {
        "prompt_id": "example-prompt-123",
        "status": "processing",
        "is_alive": True,
        "waiting_for_inputs": ["block_1", "block_2"],
        "waiting_for_outputs": [],
        "executing_blocks": ["block_3"],
        "timestamp": time.time()
    }
    print(json.dumps(health_response, indent=2))
    
    print("\nAlive Signal Format:")
    alive_signal = {
        "status": "alive",
        "waiting_for_server": True,
        "timestamp": time.time()
    }
    print(json.dumps(alive_signal, indent=2))

def demonstrate_deadlock_detection_logic():
    """
    Demonstrate the deadlock detection logic
    """
    print("\n=== Deadlock Detection Logic ===")
    
    print("Server-side detection (in ArtletServerInputBlock):")
    print("- Check every 10 seconds while waiting for inputs")
    print("- If client hasn't sent alive signal for >30s AND client is waiting for server:")
    print("  → Deadlock detected: Client appears dead")
    print("- If client is not waiting for server but server waits for inputs:")
    print("  → Potential deadlock warning")
    
    print("\nClient-side detection (in wait_for_outputs):")
    print("- Send alive signal every 5 seconds")
    print("- Check server health every 10 seconds")
    print("- If server waiting for inputs while client waits for outputs:")
    print("  → Deadlock detected: Circular dependency")
    print("- If server hasn't responded to health check for >30s:")
    print("  → Deadlock detected: Server appears dead")

def main():
    """Main test function"""
    print("Artlet Deadlock Detection Test Suite")
    print("=" * 50)
    
    # Demonstrate the concepts
    demonstrate_health_monitoring()
    demonstrate_deadlock_detection_logic()
    
    # Simulate deadlock scenarios
    simulate_deadlock_scenario_1()
    simulate_deadlock_scenario_2() 
    simulate_deadlock_scenario_3()
    
    print("\n=== Testing with Live Server (if available) ===")
    
    # Test with a live server if available
    server_address = "http://localhost:18891"
    test_prompt_id = "test-prompt-123"
    
    print(f"Testing health check route: {server_address}")
    health_data = test_health_check_route(server_address, test_prompt_id)
    
    if health_data:
        print("Server is responding to health checks")
        
        # Test alive signal
        print("Testing alive signal...")
        test_alive_signal(server_address, test_prompt_id, waiting_for_server=True)
    else:
        print("Server not available for testing")
    
    print("\n=== Test Complete ===")
    print("The deadlock detection system is now active and will:")
    print("1. Prevent infinite waits when one side dies")
    print("2. Detect circular dependencies")
    print("3. Provide detailed error messages for debugging")
    print("4. Clean up resources properly when deadlocks are detected")

if __name__ == "__main__":
    main()
