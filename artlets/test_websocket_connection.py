#!/usr/bin/env python3
"""
Test script for Artlet websocket connection functionality
"""

import sys
import os

# Add the parent directory to the path so we can import artlet modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_header_format():
    """Test the header format conversion"""
    print("Testing header format conversion...")
    
    # Simulate auth headers
    auth_headers = {
        'X-Auth-Token': 'test-token-123',
        'X-Auth-User': 'test-user',
        'Cookie': 'session=abc123'
    }
    
    # Test the new format (string list)
    header_list = []
    for k, v in auth_headers.items():
        if k is not None and v is not None:
            header_list.append(f"{k}: {v}")
    
    print(f"Original headers: {auth_headers}")
    print(f"Converted to websocket format: {header_list}")
    
    # Verify format
    expected = [
        'X-Auth-Token: test-token-123',
        'X-Auth-User: test-user', 
        'Cookie: session=abc123'
    ]
    
    if set(header_list) == set(expected):
        print("✅ Header format conversion works correctly")
    else:
        print("❌ Header format conversion failed")
        print(f"Expected: {expected}")
        print(f"Got: {header_list}")


def test_ssl_context_creation():
    """Test SSL context creation logic"""
    print("\nTesting SSL context creation...")
    
    try:
        import ssl
        
        # Test internal domain detection
        internal_hosts = [
            'server.onazure.com',
            'api.internal.company.com', 
            'localhost.local'
        ]
        
        external_hosts = [
            'api.example.com',
            'server.amazonaws.com'
        ]
        
        for host in internal_hosts:
            ssl_context = ssl.create_default_context()
            if host and ('.onazure.' in host or '.internal.' in host or '.local' in host):
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                print(f"✅ {host} - Relaxed SSL verification applied")
            else:
                print(f"❌ {host} - Should have relaxed SSL verification")
        
        for host in external_hosts:
            ssl_context = ssl.create_default_context()
            if host and ('.onazure.' in host or '.internal.' in host or '.local' in host):
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                print(f"❌ {host} - Should NOT have relaxed SSL verification")
            else:
                print(f"✅ {host} - Standard SSL verification maintained")
                
    except ImportError:
        print("❌ SSL module not available")


def test_websocket_url_construction():
    """Test websocket URL construction"""
    print("\nTesting websocket URL construction...")
    
    test_cases = [
        {
            'server_address': 'http://localhost:8188',
            'client_id': 'test-client-123',
            'expected_ws_url': 'ws://localhost:8188/ws?clientId=test-client-123'
        },
        {
            'server_address': 'https://api.example.com:443',
            'client_id': 'secure-client',
            'expected_ws_url': 'wss://api.example.com:443/ws?clientId=secure-client'
        }
    ]
    
    # Import the parse_server_address function
    try:
        from artlets.artlet_models import parse_server_address
        
        for test_case in test_cases:
            protocol, host, port = parse_server_address(test_case['server_address'])
            ws_protocol = "ws" if protocol == "http" else "wss"
            ws_url = f"{ws_protocol}://{host}:{port}/ws?clientId={test_case['client_id']}"
            
            if ws_url == test_case['expected_ws_url']:
                print(f"✅ {test_case['server_address']} -> {ws_url}")
            else:
                print(f"❌ {test_case['server_address']} -> {ws_url}")
                print(f"   Expected: {test_case['expected_ws_url']}")
                
    except ImportError as e:
        print(f"❌ Could not import parse_server_address: {e}")


def test_mock_websocket_connection():
    """Test mock websocket connection without actually connecting"""
    print("\nTesting mock websocket connection setup...")
    
    try:
        # Mock the websocket module to avoid actual connection
        class MockWebSocket:
            def __init__(self, url, header=None, sslopt=None):
                self.url = url
                self.header = header or []
                self.sslopt = sslopt
                print(f"Mock WebSocket created:")
                print(f"  URL: {url}")
                print(f"  Headers: {header}")
                print(f"  SSL Options: {sslopt}")
            
            def close(self):
                print("Mock WebSocket closed")
        
        # Test the connection logic without actual websocket
        auth_headers = {'X-Auth-Token': 'test-token'}
        header_list = [f"{k}: {v}" for k, v in auth_headers.items()]
        
        # Mock SSL context
        class MockSSLContext:
            def __init__(self):
                self.check_hostname = True
                self.verify_mode = 'CERT_REQUIRED'
        
        ssl_context = MockSSLContext()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = 'CERT_NONE'
        
        # Create mock websocket
        ws_url = "wss://test.example.com/ws?clientId=test"
        mock_ws = MockWebSocket(ws_url, header=header_list, sslopt={"context": ssl_context})
        
        print("✅ Mock websocket connection setup successful")
        
        mock_ws.close()
        
    except Exception as e:
        print(f"❌ Mock websocket connection failed: {e}")


if __name__ == "__main__":
    print("=== Artlet Websocket Connection Tests ===")
    
    test_header_format()
    test_ssl_context_creation()
    test_websocket_url_construction()
    test_mock_websocket_connection()
    
    print("\n=== Tests completed ===")
