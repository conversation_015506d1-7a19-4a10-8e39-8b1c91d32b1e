# Artlet Deadlock Detection System

## Overview

The Artlet deadlock detection system prevents ArtletClient and ArtletServer from getting stuck in infinite wait loops when one side dies or when circular dependencies occur. This system uses periodic health checks, alive signals, and timeout mechanisms to detect and resolve deadlock situations.

## Problem Statement

ArtletClient and ArtletServer can experience deadlocks in several scenarios:

1. **Client Death**: Server waits for inputs while client has crashed
2. **Server Death**: Client waits for outputs while server has crashed  
3. **Circular Dependencies**: Both sides wait for each other
4. **Network Issues**: Communication failures causing indefinite waits

## Solution Components

### 1. Health Check Routes

**Server Health Check**
- `GET /artlet/health/{prompt_id}` - Returns current execution status
- Response includes:
  - Blocks waiting for inputs
  - Blocks waiting for outputs  
  - Currently executing blocks
  - Timestamp of last activity

**Client Alive Signal**
- `POST /artlet/health/{prompt_id}/alive` - Client sends periodic alive signals
- Includes:
  - Client status ("alive")
  - Whether client is waiting for server
  - Timestamp

### 2. Periodic Health Monitoring

**Server-Side (ArtletServerInputBlock)**
- Checks client health every 10 seconds while waiting for inputs
- Detects if client hasn't sent alive signal for >30 seconds
- Throws exception if client appears dead

**Client-Side (wait_for_outputs)**
- Sends alive signal every 5 seconds
- Checks server health every 10 seconds
- Detects if server is unresponsive or has circular dependencies

### 3. Websocket Alive Signals

**ArtletWebsocketProxy**
- Sends periodic alive signals via websocket every 5 seconds
- Provides real-time communication channel for health monitoring
- Handles websocket timeouts gracefully

## Implementation Details

### Server-Side Health Check (ArtletServerInputBlock)

```python
# In the wait loop for inputs
while current_block.all_input_status != Status.READY:
    # ... existing wait logic ...
    
    # Periodic health check every 10 seconds
    if current_time - last_health_check > health_check_interval:
        prompt_data = artlet_server_data[client_id].prompts.get(prompt_id)
        if prompt_data and prompt_data.client_health:
            client_health = prompt_data.client_health
            time_since_alive = current_time - client_health.get('last_alive', 0)
            
            # Detect dead client
            if (time_since_alive > 30.0 and 
                client_health.get('waiting_for_server', False)):
                raise Exception("Deadlock detected: Client appears to be dead")
```

### Client-Side Health Check (wait_for_outputs)

```python
# In the wait loop for outputs
while True:
    # ... existing wait logic ...
    
    # Send alive signal every 5 seconds
    if current_time - last_alive_signal > alive_signal_interval:
        self.send_alive_signal(client_data, server_address, server_prompt_id, 
                              waiting_for_server=True)
    
    # Check server health every 10 seconds
    if current_time - last_health_check > health_check_interval:
        server_health = self.check_server_health(client_data, server_address, 
                                                server_prompt_id)
        if server_health:
            # Detect circular dependency
            if (len(server_health.get('waiting_for_inputs', [])) > 0 and 
                len(server_health.get('executing_blocks', [])) == 0):
                raise Exception("Deadlock detected: Server waiting for inputs while client waits for outputs")
```

## Deadlock Scenarios and Detection

### Scenario 1: Client Death
- **Situation**: Server waits for inputs, client crashes
- **Detection**: Server notices no alive signals for >30 seconds
- **Action**: Server throws exception and cleans up

### Scenario 2: Server Death  
- **Situation**: Client waits for outputs, server crashes
- **Detection**: Client health check fails or times out
- **Action**: Client throws exception and cleans up

### Scenario 3: Circular Dependency
- **Situation**: Client waits for outputs while server waits for inputs
- **Detection**: Client sees server waiting for inputs while client waits for outputs
- **Action**: Client throws exception with detailed error message

### Scenario 4: Network Issues
- **Situation**: Communication failures cause timeouts
- **Detection**: Health checks fail consistently
- **Action**: Both sides detect communication failure and clean up

## Configuration

### Timeouts
- **Health Check Interval**: 10 seconds (server and client)
- **Alive Signal Interval**: 5 seconds (client and websocket)
- **Deadlock Timeout**: 30 seconds (no alive signals)
- **HTTP Timeout**: 5 seconds (health check requests)

### Error Messages
- Clear, descriptive error messages for each deadlock type
- Include timing information and affected blocks
- Provide debugging information for troubleshooting

## Testing

Use the test script to verify deadlock detection:

```bash
python artlets/test_deadlock_detection.py
```

The test script demonstrates:
- Health check route functionality
- Alive signal transmission
- Deadlock scenario simulations
- Error message formats

## Benefits

1. **Prevents Infinite Waits**: No more hanging processes when one side dies
2. **Early Detection**: Identifies problems within 30 seconds maximum
3. **Clear Error Messages**: Detailed information for debugging
4. **Automatic Cleanup**: Proper resource cleanup when deadlocks occur
5. **Minimal Overhead**: Lightweight health checks don't impact performance

## Future Enhancements

1. **Configurable Timeouts**: Allow customization of timeout values
2. **Health Check Dashboard**: Web interface for monitoring Artlet health
3. **Automatic Recovery**: Attempt to restart failed connections
4. **Metrics Collection**: Track deadlock frequency and types
5. **Load Balancing**: Redirect to healthy servers when deadlocks occur
