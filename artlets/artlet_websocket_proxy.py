import json
import logging
import threading
import websocket
from typing import Dict, Any, Optional

# ANSI color codes for terminal output
class Colors:
    YELLOW = '\033[33m'    # For server logs
    GREEN = '\033[32m'     # For success messages
    RED = '\033[31m'       # For error messages
    BLUE = '\033[34m'      # For info messages
    CYAN = '\033[36m'      # For debug messages
    RESET = '\033[0m'      # Reset to default color


class ArtletMessageTransformer:
    """Transforms websocket messages from remote Artlet server to local frontend format"""
    
    def __init__(self, local_prompt_id: str, remote_prompt_id: str,
                 local_client_id: str, remote_client_id: str,
                 run_nodes_on_server: bool, local_node_id: Optional[str] = None):
        self.local_prompt_id = local_prompt_id
        self.remote_prompt_id = remote_prompt_id
        self.local_client_id = local_client_id
        self.remote_client_id = remote_client_id
        self.run_nodes_on_server = run_nodes_on_server
        self.local_client_node_id = local_node_id
    
        
    def transform_message(self, event: str, data: Dict[str, Any]) -> tuple[str, Dict[str, Any]]:
        """Transform remote server message to local frontend format"""
        transformed_data = data.copy()

        # Always transform prompt_id if present
        if 'prompt_id' in transformed_data:
            if transformed_data['prompt_id'] == self.remote_prompt_id:
                transformed_data['prompt_id'] = self.local_prompt_id

        # Transform node IDs only if run_nodes_on_server is False
        # If run_nodes_on_server is True, nodes are actually running on server, so keep original node IDs
        if not self.run_nodes_on_server:
            # Transform node IDs to local node ID (from UNIQUE_ID)
            if 'node' in transformed_data and self.local_client_node_id:
                transformed_data['node'] = self.local_client_node_id

            if 'display_node' in transformed_data and self.local_client_node_id:
                transformed_data['display_node'] = self.local_client_node_id

            # Transform node lists (for execution_cached messages)
            if 'nodes' in transformed_data and isinstance(transformed_data['nodes'], list) and self.local_client_node_id:
                # Replace all remote node IDs with local node ID
                transformed_data['nodes'] = [self.local_client_node_id] * len(transformed_data['nodes'])

            # Transform executed list (for error messages)
            if 'executed' in transformed_data and isinstance(transformed_data['executed'], list) and self.local_client_node_id:
                # Replace all remote node IDs with local node ID
                transformed_data['executed'] = [self.local_client_node_id] * len(transformed_data['executed'])

            # Transform node_id in error messages
            if 'node_id' in transformed_data and self.local_client_node_id:
                transformed_data['node_id'] = self.local_client_node_id

        # If run_nodes_on_server is True, don't transform node IDs - keep them as-is
        # This allows the frontend to show the actual server nodes that are executing

        return event, transformed_data


class ArtletWebsocketProxy:
    """Proxies websocket messages from remote Artlet server to local ComfyUI frontend"""

    def __init__(self, server_instance, local_client_id: str, remote_websocket,
                 transformer: ArtletMessageTransformer):
        self.server = server_instance
        self.local_client_id = local_client_id
        self.remote_ws = remote_websocket
        self.transformer = transformer
        self.running = False
        self.proxy_thread = None
        self.subscribed_to_logs = False
        self.current_log_line = 'SERVER LOGS: '
        
    def start_proxying(self):
        """Start listening to remote websocket and proxy messages"""
        if self.running:
            return

        self.running = True
        self.proxy_thread = threading.Thread(target=self._proxy_loop, daemon=True)
        self.proxy_thread.start()

        # Automatically subscribe to remote logs
        self.subscribe_to_remote_logs()

        print(f"Started Artlet websocket proxy for client {self.local_client_id}")
        
    def stop_proxying(self):
        """Stop proxying and cleanup"""
        if not self.running:
            return

        self.running = False

        # Unsubscribe from remote logs before closing
        self.unsubscribe_from_remote_logs()

        if self.remote_ws:
            try:
                self.remote_ws.close()
            except Exception as e:
                print(f"Error closing remote websocket: {e}")

        if self.proxy_thread and self.proxy_thread.is_alive():
            self.proxy_thread.join(timeout=5.0)

        print(f"Stopped Artlet websocket proxy for client {self.local_client_id}")

    def subscribe_to_remote_logs(self, server_address: Optional[str] = None):
        """Subscribe to logs from the remote server via HTTP API"""
        if self.subscribed_to_logs:
            return

        try:
            # Note: Log subscription is done via HTTP PATCH to /internal/logs/subscribe
            # This would need to be implemented in the ArtletClientBlock that has access to server_address
            # For now, we'll just mark as subscribed and rely on the remote server to send logs
            # if it has any active log subscriptions
            self.subscribed_to_logs = True
            print(f"Marked as subscribed to remote logs for client {self.local_client_id}")
            print("Note: Actual HTTP subscription to remote logs should be done by ArtletClientBlock")
        except Exception as e:
            print(f"Failed to subscribe to remote logs: {e}")

    def unsubscribe_from_remote_logs(self):
        """Unsubscribe from logs from the remote server"""
        if not self.subscribed_to_logs:
            return

        try:
            # Note: Log unsubscription would be done via HTTP PATCH to /internal/logs/subscribe
            # For now, we'll just mark as unsubscribed
            self.subscribed_to_logs = False
            print(f"Marked as unsubscribed from remote logs for client {self.local_client_id}")
            print("Note: Actual HTTP unsubscription from remote logs should be done by ArtletClientBlock")
        except Exception as e:
            print(f"Failed to unsubscribe from remote logs: {e}")

    def _proxy_loop(self):
        """Main proxy loop that receives and forwards messages"""
        try:
            while self.running and self.remote_ws:
                try:
                    # Set a timeout to allow checking self.running periodically
                    self.remote_ws.settimeout(1.0)
                    message = self.remote_ws.recv()
                    
                    if not message:
                        continue
                        
                    if isinstance(message, str):
                        try:
                            parsed_message = json.loads(message)
                            event = parsed_message.get('type')
                            data = parsed_message.get('data', {})
                            
                            if event:
                                # Handle logs messages specially - forward as-is without transformation
                                if event == "logs":
                                    # Forward logs directly to local frontend we will print it so anyway it will go as this servers log which does the work for differenciation we will add SERVER LOGS: prefix
                                    log_data = data.get('entries', [])
                                    log_data = [ entry.get('m', '') for entry in log_data ]
                                    log_data = ''.join(log_data)
                                    self.current_log_line += log_data
                                    if '\n' in self.current_log_line:
                                        # Print in yellow color for better visibility on client
                                        print(f"{Colors.YELLOW}{self.current_log_line.rstrip()}{Colors.RESET}")
                                        self.current_log_line = 'SERVER LOGS: ' # reset the current log line
                                else:
                                    # Transform other messages for local frontend
                                    transformed_event, transformed_data = self.transformer.transform_message(event, data)

                                    # Send to local frontend using standard send_sync
                                    self.server.send_sync(transformed_event, transformed_data, self.local_client_id)
                            
                        except json.JSONDecodeError as e:
                            print(f"Failed to parse websocket message: {e}")
                            
                    elif isinstance(message, (bytes, bytearray)):
                        # Handle binary messages using ComfyUI's binary format
                        # ComfyUI binary format: [4-byte event type][binary data]
                        try:
                            import struct

                            if len(message) >= 4:
                                # Decode the ComfyUI binary message format
                                # First 4 bytes are the event type (big-endian unsigned int)
                                event_bytes = message[:4]
                                binary_data = message[4:]
                                event_type = struct.unpack(">I", event_bytes)[0]

                                print(f"Received binary message: event_type={event_type}, data_length={len(binary_data)}")

                                # Check if this is a known ComfyUI binary event type that might contain transformable data
                                from server import BinaryEventTypes

                                # if event_type == BinaryEventTypes.PROGRESS_TEXT:
                                #     # Progress text format: [4-byte node_id_length][node_id_string][text_string]
                                #     try:
                                #         if len(binary_data) >= 4:
                                #             node_id_length = struct.unpack(">I", binary_data[:4])[0]
                                #             if node_id_length > 0 and len(binary_data) >= 4 + node_id_length:
                                #                 node_id_bytes = binary_data[4:4+node_id_length]
                                #                 text_bytes = binary_data[4+node_id_length:]

                                #                 # Decode node ID and transform it
                                #                 remote_node_id = node_id_bytes.decode('utf-8')
                                #                 text = text_bytes.decode('utf-8')

                                #                 # Transform node ID if we have a mapping
                                #                 local_node_id = self.transformer.node_id_mapping.get(remote_node_id, remote_node_id)

                                #                 # Reconstruct the progress text message
                                #                 local_node_id_bytes = local_node_id.encode('utf-8')
                                #                 new_node_id_length = len(local_node_id_bytes)
                                #                 new_binary_data = struct.pack(">I", new_node_id_length) + local_node_id_bytes + text_bytes

                                #                 # Send the transformed message
                                #                 self.server.send_sync(event_type, new_binary_data, self.local_client_id)
                                #                 print(f"Proxied progress text: {remote_node_id} -> {local_node_id}")
                                #                 continue
                                #     except (UnicodeDecodeError, struct.error) as e:
                                #         print(f"Failed to parse progress text message: {e}")

                                # For other binary messages (like preview images), forward as-is
                                # These typically don't contain node/prompt IDs that need transformation
                                self.server.send_sync(event_type, binary_data, self.local_client_id)
                                print(f"Proxied binary message: event_type={event_type}, size={len(binary_data)} bytes")

                            else:
                                print(f"Binary message too short: {len(message)} bytes")

                        except Exception as e:
                            print(f"Error processing binary message: {e}")
                            # Try to forward the message as-is
                            try:
                                from server import BinaryEventTypes
                                # If we can't decode it properly, treat it as a preview image
                                self.server.send_sync(BinaryEventTypes.PREVIEW_IMAGE, message[4:] if len(message) > 4 else message, self.local_client_id)
                            except Exception as e2:
                                print(f"Failed to forward binary message: {e2}")
                    else:
                        print(f"Received unexpected message type from remote server: {type(message)}")
                        
                except websocket.WebSocketTimeoutException:
                    # Timeout is expected, continue loop
                    continue
                except websocket.WebSocketConnectionClosedException:
                    print("Remote websocket connection closed")
                    break
                except Exception as e:
                    print(f"Error in websocket proxy loop: {e}")
                    break
                    
        except Exception as e:
            print(f"Fatal error in websocket proxy: {e}")
        finally:
            self.running = False


def create_artlet_websocket_proxy(server_instance, local_client_id: str, local_prompt_id: str,
                                remote_websocket, remote_prompt_id: str, remote_client_id: str,
                                run_nodes_on_server: bool, local_node_id: Optional[str] = None) -> ArtletWebsocketProxy:
    """Factory function to create an Artlet websocket proxy"""

    transformer = ArtletMessageTransformer(
        local_prompt_id=local_prompt_id,
        remote_prompt_id=remote_prompt_id,
        local_client_id=local_client_id,
        remote_client_id=remote_client_id,
        run_nodes_on_server=run_nodes_on_server,
        local_node_id=local_node_id
    )

    proxy = ArtletWebsocketProxy(
        server_instance=server_instance,
        local_client_id=local_client_id,
        remote_websocket=remote_websocket,
        transformer=transformer
    )

    return proxy
