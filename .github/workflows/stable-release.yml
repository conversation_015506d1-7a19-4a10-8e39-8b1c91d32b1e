
name: "Release Stable Version"

on:
  workflow_dispatch:
    inputs:
      git_tag:
        description: 'Git tag'
        required: true
        type: string
      cu:
        description: 'CUDA version'
        required: true
        type: string
        default: "126"
      python_minor:
        description: 'Python minor version'
        required: true
        type: string
        default: "12"
      python_patch:
        description: 'Python patch version'
        required: true
        type: string
        default: "9"


jobs:
  package_comfy_windows:
    permissions:
      contents: "write"
      packages: "write"
      pull-requests: "read"
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.git_tag }}
          fetch-depth: 0
          persist-credentials: false
      - uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            cu${{ inputs.cu }}_python_deps.tar
            update_comfyui_and_python_dependencies.bat
          key: ${{ runner.os }}-build-cu${{ inputs.cu }}-${{ inputs.python_minor }}
      - shell: bash
        run: |
          mv cu${{ inputs.cu }}_python_deps.tar ../
          mv update_comfyui_and_python_dependencies.bat ../
          cd ..
          tar xf cu${{ inputs.cu }}_python_deps.tar
          pwd
          ls

      - shell: bash
        run: |
          cd ..
          cp -r ComfyUI ComfyUI_copy
          curl https://www.python.org/ftp/python/3.${{ inputs.python_minor }}.${{ inputs.python_patch }}/python-3.${{ inputs.python_minor }}.${{ inputs.python_patch }}-embed-amd64.zip -o python_embeded.zip
          unzip python_embeded.zip -d python_embeded
          cd python_embeded
          echo ${{ env.MINOR_VERSION }}
          echo 'import site' >> ./python3${{ inputs.python_minor }}._pth
          curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
          ./python.exe get-pip.py
          ./python.exe -s -m pip install ../cu${{ inputs.cu }}_python_deps/*
            sed -i '1i../ComfyUI' ./python3${{ inputs.python_minor }}._pth
            cd ..

          git clone --depth 1 https://github.com/comfyanonymous/taesd
          cp taesd/*.pth ./ComfyUI_copy/models/vae_approx/

          mkdir ComfyUI_windows_portable
          mv python_embeded ComfyUI_windows_portable
          mv ComfyUI_copy ComfyUI_windows_portable/ComfyUI

          cd ComfyUI_windows_portable

          mkdir update
          cp -r ComfyUI/.ci/update_windows/* ./update/
          cp -r ComfyUI/.ci/windows_base_files/* ./
          cp ../update_comfyui_and_python_dependencies.bat ./update/

          cd ..

          "C:\Program Files\7-Zip\7z.exe" a -t7z -m0=lzma2 -mx=8 -mfb=64 -md=32m -ms=on -mf=BCJ2 ComfyUI_windows_portable.7z ComfyUI_windows_portable
          mv ComfyUI_windows_portable.7z ComfyUI/ComfyUI_windows_portable_nvidia.7z

          cd ComfyUI_windows_portable
          python_embeded/python.exe -s ComfyUI/main.py --quick-test-for-ci --cpu

          ls

      - name: Upload binaries to release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: ComfyUI_windows_portable_nvidia.7z
          tag: ${{ inputs.git_tag }}
          overwrite: true
          prerelease: true
          make_latest: false
