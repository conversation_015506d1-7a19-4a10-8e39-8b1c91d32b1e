absl-py==2.1.0
accelerate==1.1.1
addict==2.4.0
aggdraw==1.3.18.post0
aiofiles==23.2.1
aiohttp==3.9.5
aioresponses==0.7.6
aiosignal==1.3.1
albucore==0.0.17
albumentations==1.4.18
altair==5.3.0
annotated-types==0.7.0
anthropic==0.32.0
antlr4-python3-runtime==4.9.3
anyio==4.4.0
APScheduler==3.10.1
argcomplete==3.5.1
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
argostranslate==1.9.6
arrow==1.3.0
asgiref==3.8.1
asttokens==2.4.1
async-lru==2.0.4
async-timeout==4.0.3
asyncio==3.4.3
attrs==23.2.0
audioread==3.0.1
#auto_gptq==0.7.1
av==11.0.0
awscli==1.33.33
Babel==2.15.0
backoff==2.2.1
bcrypt==4.1.3
beartype==0.19.0
beautifulsoup4==4.12.3
bert-score==0.3.13
binaryornot==0.4.4
bitsandbytes==0.43.1
black==23.7.0
bleach==6.1.0
blend_modes==2.1.0
blind-watermark==0.4.4
blinker==1.8.2
blis==0.7.11
blosc2==2.7.0
boto3==1.34.86
botocore==1.34.151
braceexpand==0.1.7
Brotli==1.0.9
bs4==0.0.2
build==1.2.1
cachetools==5.4.0
catalogue==2.0.10
certifi==2024.6.2
cffi==1.16.0
cfgv==3.4.0
chardet==4.0.0
charset-normalizer==3.3.2
chroma-hnswlib==0.7.3
chromadb==0.5.3
chumpy==0.70
click==8.1.7
clip==1.0
clip-interrogator==0.6.0
cloudpathlib==0.16.0
cloudpickle==3.0.0
cmake==3.29.3
color-matcher==0.5.0
colorama==0.4.4
coloredlogs==15.0.1
colorlog==6.8.2
colour-science==0.4.4
comm==0.2.2
confection==0.1.5
configobj==5.0.8
contexttimer==0.3.3
contourpy==1.2.1
controlnet-aux==0.0.6
cookiecutter==2.6.0
coverage==7.5.4
cpm-kernels==1.0.11
cryptography==42.0.8
cssselect2==0.7.0
cstr==0.1.0
ctranslate2==4.3.1
cubinlinker-cu11==0.3.0.post2
cuda-python==11.8.3
cudf-cu11==24.4.1
cuml-cu11==24.4.0
cupy-cuda11x==13.1.0
cycler==0.12.1
cymem==2.0.8
Cython==3.0.0
dadaptation==3.2
dask==2024.1.1
dask-cuda==24.4.0
dask-cudf-cu11==24.4.1
dask-expr==0.4.0
dataclasses-json==0.6.6
datamodel-code-generator==0.26.1
datasets==2.20.0
ddt==1.7.2
debugpy==1.8.1
decorator==4.4.2
decord==0.6.0
deep-translator==1.11.4
deepdiff==7.0.1
deepdish==0.3.7
deepspeed==0.15.4
defusedxml==0.7.1
Deprecated==1.2.14
diffusers==0.31.0
dill==0.3.8
diskcache==5.6.3
distlib==0.3.8
distributed==2024.1.1
distro==1.9.0
dlib==19.24.6
dnspython==2.6.1
docker==6.1.3
docker-pycreds==0.4.0
docopt==0.6.2
docstring_parser==0.16
docutils==0.16
easydict==1.13
easygui==0.98.3
einops==0.8.0
email_validator==2.2.0
embreex==2.17.7.post4
eval_type_backport==0.2.0
evalidate==2.0.3
exceptiongroup==1.2.2
executing==2.0.1
fabric==3.2.2
fairscale==0.4.13
fal_client==0.4.1
fastapi==0.110.0
fastapi-cli==0.0.4
faster-whisper==1.0.3
fastjsonschema==2.19.1
fastrlock==0.8.2
ffmpeg-python==0.2.0
ffmpy==0.3.0
filelock==3.13.1
filetype==1.2.0
filterpy==1.4.5
fire==0.5.0
flake8==7.1.0
flatbuffers==24.3.25
flet==0.23.2
flet-core==0.23.2
flet-runtime==0.23.2
fonttools==4.53.1
fqdn==1.5.1
freetype-py==2.4.0
frozenlist==1.4.1
fsspec==2024.2.0
ftfy==6.2.0
future==1.0.0
fvcore==0.1.5.post20221221
galore-torch==1.0
gdown==5.1.0
gekko==1.2.1
genson==1.3.0
gguf==0.10.0
gitdb==4.0.11
GitPython==3.1.43
glfw==2.7.0
glitch_this==1.0.3
gmpy2==2.1.2
google==3.0.0
google-ai-generativelanguage==0.6.6
google-api-core==2.19.0
google-api-python-client==2.131.0
google-auth==2.29.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.0.0
google-cloud-core==2.4.1
google-cloud-storage==2.16.0
google-crc32c==1.5.0
google-generativeai==0.7.2
google-resumable-media==2.7.0
googleapis-common-protos==1.63.0
googletrans-py==4.0.0
GPUtil==1.4.0
gradio==3.41.2
gradio_client==0.5.0
grpcio==1.64.0
grpcio-status==1.62.3
h11==0.14.0
h2==4.1.0
h5py==3.10.0
hjson==3.1.0
hpack==4.0.0
httpcore==1.0.5
httplib2==0.22.0
httptools==0.6.1
httpx==0.27.0
httpx-sse==0.4.0
hub-sdk==0.0.8
huggingface-hub==0.25.2
humanfriendly==10.0
hydra-core==1.3.2
hyperframe==6.0.1
identify==2.5.36
idna==3.6
image-reward==1.5
imageio==2.34.0
imageio-ffmpeg==0.5.1
imath==0.0.2
img2texture==1.0.6
importlib_metadata==7.1.0
importlib_resources==6.4.0
inference-cli==0.9.13
inference-gpu==0.13.0
inference-sdk==0.12.1
inflect==5.6.2
iniconfig==2.0.0
insightface==0.7.3
instructor==1.5.2
interrogate==1.7.0
invisible-watermark==0.2.0
invoke==2.2.0
iopath==0.1.10
ipykernel==6.29.4
ipython==8.25.0
ipywidgets==8.1.3
isoduration==20.11.0
isort==4.3.21
jax==0.4.28
jaxlib==0.4.28
jedi==0.19.1
Jinja2==3.1.4
jiter==0.5.0
jmespath==1.0.1
joblib==1.4.2
json-lines==0.5.0
json_repair==0.30.0
json-tricks==3.17.3
json5==0.9.25
jsonpointer==2.4
jsonschema==4.22.0
jsonschema-specifications==2023.12.1
jupyter==1.0.0
jupyter_client==8.6.2
jupyter-console==6.6.3
jupyter_core==5.7.2
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_server==2.14.1
jupyter_server_terminals==0.5.3
jupyterlab==4.2.1
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.2
jupyterlab_widgets==3.0.11
kaldi-native-fbank==1.20.0
kiwisolver==1.4.5
kornia==0.7.3
kornia_rs==0.1.5
kubernetes==30.1.0
langcodes==3.4.0
language_data==1.2.0
lark-parser==0.12.0
lazy_loader==0.4
librosa==0.10.2.post1
lightning-utilities==0.11.6
linkify-it-py==2.0.3
lion-pytorch==0.2.2
lit==18.1.8
litellm==1.41.14
llama_cpp_python==0.2.77
llvmlite==0.42.0
lmdb==1.5.1
locket==1.0.0
loguru==0.7.2
loralib==0.1.2
ltx-video==0.1.2
lxml==5.2.2
lycoris-lora==3.1.0
mapbox-earcut==1.0.1
marisa-trie==1.1.1
Markdown==3.6
markdown-it-py==3.0.0
MarkupSafe==2.1.3
marshmallow==3.21.2
matplotlib==3.7.5
matplotlib-inline==0.1.7
matrix-client==0.4.0
mccabe==0.7.0
mdit-py-plugins==0.3.3
mdurl==0.1.2
mediapipe==0.10.18
mistune==3.0.2
mkl-fft==1.3.8
mkl-random==1.2.4
mkl-service==2.4.0
ml-dtypes==0.4.0
mmengine==0.10.4
mmh3==4.1.0
modelscope==1.16.1
monotonic==1.6
motmetrics==1.4.0
moviepy==1.0.3
mpmath==1.3.0
msgpack==1.0.8
mss==9.0.1
multidict==6.0.5
multiprocess==0.70.16
munkres==1.1.4
murmurhash==1.0.10
mypy-extensions==1.0.0
natsort==8.4.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
ndindex==1.8
nest-asyncio==1.6.0
networkx==3.2.1
ninja==********
nltk==3.8.1
nodeenv==1.9.0
notebook==7.2.0
notebook_shim==0.2.4
numba==0.59.1
numexpr==2.10.1
numpy==1.26.4
nvidia-cublas-cu11==**********
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu11==11.7.101
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu11==11.7.99
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu11==11.7.99
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu11==********
nvidia-cudnn-cu12==********
nvidia-cufft-cu11==*********
nvidia-cufft-cu12==*********
nvidia-curand-cu11==**********
nvidia-curand-cu12==**********
nvidia-cusolver-cu11==********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu11==*********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.555.43
nvidia-nccl-cu11==2.14.3
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.99
nvidia-nvtx-cu11==11.7.91
nvidia-nvtx-cu12==12.1.105
nvtx==0.2.10
oauthlib==3.2.2
ollama==0.3.3
omegaconf==2.3.0
onnx==1.16.1
onnxruntime==1.18.0
onnxruntime-gpu==1.17.1
open-clip-torch==2.22.0
openai==1.51.2
opencv-contrib-python==*********
opencv-contrib-python-headless==*********
opencv-python==*********
opencv-python-headless==*********
OpenEXR==3.2.4
opentelemetry-api==1.25.0
opentelemetry-exporter-otlp-proto-common==1.25.0
opentelemetry-exporter-otlp-proto-grpc==1.25.0
opentelemetry-instrumentation==0.46b0
opentelemetry-instrumentation-asgi==0.46b0
opentelemetry-instrumentation-fastapi==0.46b0
opentelemetry-proto==1.25.0
opentelemetry-sdk==1.25.0
opentelemetry-semantic-conventions==0.46b0
opentelemetry-util-http==0.46b0
opt-einsum==3.3.0
optimum==1.22.0
ordered-set==4.1.0
orjson==3.10.6
overrides==7.7.0
packaging==23.2
pandas==2.0.3
pandocfilters==1.5.1
parameterized==0.9.0
paramiko==3.4.0
parso==0.8.4
partd==1.4.2
pathspec==0.12.1
pdf2image==1.17.0
peft==0.11.1
pendulum==3.0.0
pexpect==4.9.0
photoshop-connection==0.2.0
piexif==1.1.3
pilgram==1.2.1
pillow==10.2.0
pip==24.0
pixeloe==0.0.10
platformdirs==4.2.2
plotly==5.22.0
pluggy==1.5.0
plumbum==1.8.3
plyfile==1.0.3
pooch==1.8.1
portalocker==2.8.2
poseval==0.1.0
posthog==3.5.0
pre-commit==3.7.1
preshed==3.0.9
prettytable==3.10.0
prodigyopt==1.0
proglog==0.1.10
prometheus_client==0.20.0
prometheus-fastapi-instrumentator==6.0.0
prompt_toolkit==3.0.45
proto-plus==1.23.0
protobuf==4.25.5
psd-tools==1.9.34
psutil==6.0.0
ptxcompiler-cu11==0.8.1.post1
ptyprocess==0.7.0
pudb==2024.1.2
PuLP==2.8.0
pure-eval==0.2.2
py==1.11.0
py-cpuinfo==9.0.0
pyarrow==17.0.0
pyarrow-hotfix==0.6
pyasn1==0.6.0
pyasn1_modules==0.4.0
pyav==12.3.0
pybase64==1.3.2
pybboxes==0.1.6
pycocoevalcap==1.2
pycocotools==2.0.8
pycodestyle==2.12.0
pycollada==0.8
pycparser==2.22
pydantic==2.8.2
pydantic_core==2.20.1
pydantic-settings==2.3.1
pydeck==0.9.1
pyDeprecate==0.3.1
pydot==2.0.0
pydub==0.25.1
pyflakes==3.2.0
PyGithub==2.3.0
pyglet==1.5.27
Pygments==2.18.0
PyJWT==2.8.0
pylibraft-cu11==24.4.0
PyMatting==1.1.12
PyMuPDF==1.24.10
PyMuPDFb==1.24.10
PyNaCl==1.5.0
pynvml==11.4.1
PyOpenGL==3.1.0
pyOpenSSL==24.1.0
pyparsing==3.1.2
PyPDF2==3.0.1
PyPika==0.48.9
pypng==0.20220715.0
pyproject_hooks==1.1.0
pyre-extensions==0.0.23
pyrender==0.1.45
PySocks==1.7.1
pyspng==0.1.1
pytest==8.2.1
pytest-asyncio==0.21.1
pytest-runner==6.0.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==2.0.7
python-multipart==0.0.9
python-slugify==8.0.4
pytorch-lightning==2.4.0
pytorch-triton==3.0.0+dedb7bdf33
pytorchvideo==0.1.5
pytz==2024.1
PyWavelets==1.6.0
PyYAML==6.0.1
pyzbar==0.1.9
pyzmq==26.0.3
qrcode==7.4.2
qtconsole==5.5.2
QtPy==2.4.1
qudida==0.0.4
raft-dask-cu11==24.4.0
ram==0.0.1
rapids-dask-dependency==24.4.1
ray==2.23.0
redis==5.0.4
referencing==0.35.1
regex==2023.12.25
rembg==2.0.57
repath==0.9.0
reportlab==4.2.0
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
requirements-parser==0.11.0
rf-clip==1.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.7.1
rmm-cu11==24.4.0
roboflow==1.1.37
rotary-embedding-torch==0.8.5
rouge==1.0.1
rpds-py==0.18.1
rpyc==6.0.0
rsa==4.7.2
Rtree==1.2.0
ruff==0.5.5
s3transfer==0.10.2
sacremoses==0.0.53
safetensors==0.4.5
sahi==0.11.18
scenedetect==0.6.4
scikit-build==0.17.6
scikit-image==0.24.0
scikit-learn==1.3.2
scikit-video==1.1.11
scipy==1.14.1
seaborn==0.13.2
segment-anything==1.0
semantic-version==2.10.0
Send2Trash==1.8.3
sensevoice-onnx==1.1.0
sentencepiece==0.2.0
sentry-sdk==2.11.0
setproctitle==1.3.3
setuptools==65.0.0
shapely==2.0.1
shellingham==1.5.4
shtab==1.7.1
simple-lama-inpainting==0.1.2
simpleeval==0.9.13
six==1.16.0
sk-video==1.1.10
skypilot==0.5.0
smart-open==6.4.0
smmap==5.0.1
smplx==0.1.28
sniffio==1.3.1
sortedcontainers==2.4.0
sounddevice==0.4.7
soundfile==0.12.1
soupsieve==2.5
soxr==0.3.7
spacy==3.7.4
spacy-legacy==3.0.12
spacy-loggers==1.0.5
spandrel==0.3.4
srsly==2.4.8
sse-starlette==2.1.0
stack-data==0.6.3
stanza==1.1.1
starlette==0.36.3
starlette-context==0.3.6
streamlit==1.37.0
structlog==24.2.0
submitit==1.5.1
supervision==0.20.0
surrealist==1.0.0
svg.path==6.3
svglib==1.5.1
swarm==0.1.0
SwissArmyTransformer==0.4.12
sympy==1.13.1
tables==3.9.2
tabulate==0.9.0
taming-transformers-rom1504==0.0.6
tblib==3.0.0
tenacity==8.5.0
tensorboard==2.14.0
tensorboard-data-server==0.7.2
tensorboardX==2.6
termcolor==2.4.0
terminado==0.18.1
terminaltables==3.1.10
test-tube==0.7.5
text-unidecode==1.3
thinc==8.2.3
thop==0.1.1.post2209072238
threadpoolctl==3.5.0
tifffile==2024.7.24
tiktoken==0.7.0
time-machine==2.14.1
timm==0.6.13
tinycss2==1.3.0
tokenizers==0.20.3
toml==0.10.2
tomli==2.0.1
tomlkit==0.12.0
toolz==0.12.1
torch==2.1.0+cu121
torchaudio==2.1.0+cu121
torchdata==0.7.1
torchdiffeq==0.2.3
torchgeometry==0.1.2
torchmetrics==0.10.3
torchsde==0.2.6
torchvision==0.16.0+cu121
tornado==6.4.1
tqdm==4.66.5
traitlets==5.14.3
trampoline==0.1.2
transformers==4.46.3
transparent-background==1.3.1
treelite==4.1.2
trimesh==4.4.0
triton==2.1.0
typer==0.9.0
typer-config==1.4.0
types-python-dateutil==2.9.0.20240316
types-setuptools==75.6.0.20241223
typing==3.7.4.3
typing_extensions==4.11.0
typing-inspect==0.9.0
tyro==0.8.5
tzdata==2024.1
tzlocal==5.2
uc-micro-py==1.0.3
ucx-py-cu11==0.37.0
ujson==5.10.0
ultralytics==8.0.239
ultralytics-thop==0.2.7
ultralyticsplus==0.1.0
uri-template==1.3.0
uritemplate==4.1.1
urllib3==2.2.1
urwid==2.6.15
urwid_readline==0.14
uvicorn==0.27.1
uvloop==0.19.0
vhacdx==0.0.6
virtualenv==20.26.2
voluptuous==0.15.2
wandb==0.17.0
wasabi==1.1.3
watchdog==4.0.1
watchfiles==0.22.0
wcwidth==0.2.13
weasel==0.3.4
webcolors==1.13
webdataset==0.2.86
webencodings==0.5.1
websocket-client==1.8.0
websockets==11.0.3
Werkzeug==3.0.3
wget==3.2
wheel==0.43.0
widgetsnbextension==4.0.11
wrapt==1.16.0
xatlas==0.0.9
xdoctest==1.1.5
xformers==0.0.22.post7
xmltodict==0.13.0
xtcocotools==1.14.3
xxhash==3.4.1
yacs==0.1.8
yapf==0.40.2
yarl==1.9.4
zict==3.0.0
zipp==3.19.1
zxing-cpp==2.2.0
