#!/bin/bash
# This scrit is to test if check if newer version frozen_reqs.txt is working

# 1st parameter is given take it as requirements file 2nd parameter is given take it as env_name

frozen_reqs="${1:-/qsfs2/services/qsComfy/qs_reqs/frozen_requirements.txt}"
env_name="${2:-qs}"
base_env_name="${env_name}base"
echo running with $frozen_reqs and $env_name and $base_env_name
source ~/.bashrc
#check if frozen_reqs file exists
if [ ! -f "$frozen_reqs" ]; then
    echo "$frozen_reqs file not found"
    exit 1
fi
#make sure env_name is not qsbase or qs
if  [ ! $env_name == "qs" ]; then
    echo "Not using defaut env_name qs; proceed with env_name $env_name"
    read answer
    if [ ! $answer == "yes" ]; then
        exit 1
    fi
fi
# check if test_env directory exists
if [ -d "/home/<USER>/.conda/envs/$env_name" ] || [ -d "/home/<USER>/.conda/envs/${base_env_name}" ]; then
    echo "$env_name environment exist, do you want to delete and continue? (yes/no)"
    read answer
    if [ ! $answer == "yes" ]; then
        exit 1
    fi
fi
# delete current existing envs

conda env remove -n $env_name -y
conda env remove -n ${base_env_name} -y

#deactivate any current envs
eval "$(conda shell.bash hook)" && conda deactivate 2>/tmp/null; conda deactivate 2>/tmp/null; conda deactivate 2>/tmp/null 


conda create --name ${base_env_name} python=3.12.2 -y && eval "$(conda shell.bash hook)" && unset PYTHONPATH && conda activate ${base_env_name}
if [ $? -ne 0 ]; then
    echo "${base_env_name} env could not be created"
    exit 1
fi
#install torch and xformers with pip method  this breaks sometime, but no choise
#install openimageio with conda method
#following packages are causing issue to jupyter so do not use till wil need
#conda install -y conda-forge::openimageio=*******
#conda install -y -c conda-forge py-openimageio=*******

#to create frozen.txt  include frozen only
conda install conda-forge::ffmpeg

pip install torch==2.5.1+cu124 torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124
pip install torch==2.5.1+cu124 -U  xformers --index-url https://download.pytorch.org/whl/cu124
pip install torch==2.5.1+cu124 jupyter notebook #frozen only
pip install torch==2.5.1+cu124 -r /qsfs2/services/qsComfy/requirements.txt
pip install torch==2.5.1+cu124 openai omegaconf 

for dd in  qsRgt qsDynanmicW  qsLtx qsCogx qsEnricos qsCTools  ComfyUI-Florence2 ComfyUI-Manager  ComfyUI-VideoHelperSuite   ComfyUI-segment-anything-2  ComfyUI_essentials comfy_mtb comfyui-browser  comfyui-workspace-manager  was-node-suite-comfyui ; do 
    cd /qsfs2/services/qsComfy/custom_nodes/
    if [ -d "/qsfs2/services/qsComfy/custom_nodes/$dd"  ] && [ -f "/qsfs2/services/qsComfy/custom_nodes/$dd/requirements.txt" ]; then
        echo "installing $dd"
        pip install torch==2.5.1+cu124  -r /qsfs2/services/qsComfy/custom_nodes/$dd/requirements.txt ; 
    else
        echo "$dd not found or requirements.txt not found for $dd, skipping"
    fi
         echo ""
   
done

# check if torch is working 
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"
if [ $? -eq 0 ]; then 
    echo "torch working with new requirements"
else
    echo "torch not working with new requirements, do you want to delete test envs? (yes/no)"
    read answer
    if [ $answer == "yes" ]; then
        conda  env remove -n ${base_env_name} -y        
        conda env remove -n $env_name -y
    else 
        exit 1
    fi
fi
echo "base envrionment ${base_env_name} env setup is complete"

conda deactivate

#now create conda env ${env_name} and update pythonpath,

conda create --name $env_name python=3.12.2 -y

mkdir -p /home/<USER>/.conda/envs/$env_name/etc/conda/activate.d
mkdir -p /home/<USER>/.conda/envs/$env_name/etc/conda/deactivate.d

cp /qsfs2/services/qsComfy/qs_reqs/env_vars.sh /tmp/
cp /qsfs2/services/qsComfy/qs_reqs/unset_env_vars.sh /tmp/
# replace qsbase from /tmp/env_vars.sh with $qsbase_env
sed -i "s|qsbase|$base_env_name|g" /tmp/env_vars.sh
sed -i "s|qsbase|$base_env_name|g" /tmp/unset_env_vars.sh

cp /tmp/env_vars.sh /home/<USER>/.conda/envs/$env_name/etc/conda/activate.d/
chmod +x /home/<USER>/.conda/envs/$env_name/etc/conda/activate.d/env_vars.sh
cp /tmp/unset_env_vars.sh /home/<USER>/.conda/envs/$env_name/etc/conda/deactivate.d/
chmod +x /home/<USER>/.conda/envs/$env_name/etc/conda/deactivate.d/unset_env_vars.sh

unset PYTHONPATH && conda activate ${env_name}
#check if torch is working
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"
if [ $? -eq 0 ]; then 
    echo "torch working with new requirements"
else
    echo "torch not working with new requirements, do you want to delete test envs? (yes/no)"
    read answer
    if [ $answer == "yes" ]; then
        conda  env remove -n ${base_env_name} -y        
        conda env remove -n $env_name -y
    else 
        exit 1
    fi
fi

echo "new env ${base_env_name} and ${env_name} created, run comfyui and check"

exit 0
