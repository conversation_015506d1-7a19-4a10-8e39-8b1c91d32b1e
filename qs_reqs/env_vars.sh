#!/bin/bash

# Add qs site-packages to the end of PYTHONPATH
QS_PYTHONPATH="/home/<USER>/.conda/envs/qsbase/lib/python3.12/site-packages"
if [[ ":$PYTHONPATH:" != *":$QS_PYTHONPATH:"* ]]; then
    export PYTHONPATH="${PYTHONPATH:+$PYTHONPATH:}$QS_PYTHONPATH"
fi

# Add qs bin to the end of PATH
QS_BIN_PATH="/home/<USER>/.conda/envs/qsbase/bin"
if [[ ":$PATH:" != *":$QS_BIN_PATH:"* ]]; then
    export PATH="${PATH:+$PATH:}$QS_BIN_PATH"
fi
