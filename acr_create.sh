#!/bin/bash

# Azure Container Registry Name
ACR_NAME="qsacr001"
# Azure Resource Group Name (where ACR exists)
RESOURCE_GROUP="qsuser"

# Group Name for Permissions
GROUP_NAME="qsusergroup001"

echo "Step 1: Verify Azure AD Group exists and get its Object ID"
#GROUP_ID=$(az ad group show --display-name "$GROUP_NAME" --query "objectId" -o tsv 2>/dev/null)
GROUP_ID="8f84cbf6-ca4c-4e76-873a-9a09e087f6bb"

if [ -z "$GROUP_ID" ]; then
  echo "Error: Azure AD Group '$GROUP_NAME' not found. Please double-check the group name."
  echo "You can list groups using: az ad group list --display-name-starts-with '$GROUP_NAME'"
  exit 1
fi

echo "Found Group ID for '$GROUP_NAME': $GROUP_ID"

echo "Step 2: Assign ACR Roles to the Azure AD Group"

# acrpull: Allows pulling images
echo "Assigning acrpull role..."
az role assignment create --assignee "$GROUP_ID" --role acrpull --scope "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.ContainerRegistry/registries/$ACR_NAME"

# acrpush: Allows pushing images
echo "Assigning acrpush role..."
az role assignment create --assignee "$GROUP_ID" --role acrpush --scope "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.ContainerRegistry/registries/$ACR_NAME"

# acrdelete: Allows deleting images and artifacts (use with caution)
echo "Assigning acrdelete role..."
az role assignment create --assignee "$GROUP_ID" --role acrdelete --scope "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.ContainerRegistry/registries/$ACR_NAME"

echo "Permissions assigned to group '$GROUP_NAME' on ACR '$ACR_NAME'."
echo "Note: 'Create' happens on push, 'rename' of repositories is not a direct RBAC action."

echo "Step 3: Docker Push with Build Arguments (as before)"
echo "Authenticate Docker with ACR (you need to do this once):"
echo "az acr login --name $ACR_NAME"
echo "Or:"
echo "docker login $ACR_NAME.azurecr.io -u <acr_username> -p <acr_password_or_token>"
echo ""
echo "Once logged in, to push your image with build arguments, you first need to build it:"
echo "docker build --build-arg HOST_UID=$(id -u) --build-arg HOST_GID=$(id -g) -t $ACR_NAME.azurecr.io/qs-comfy:latest ."
echo ""
echo "Then, you can push the image:"
echo "docker push $ACR_NAME.azurecr.io/qs-comfy:latest"