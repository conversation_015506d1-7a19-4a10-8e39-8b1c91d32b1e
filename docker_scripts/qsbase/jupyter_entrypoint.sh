#!/bin/bash
set -e
set -x

if [[ "$1" == "jupyter" ]]; then
    exec jupyter notebook --ip 0.0.0.0 --port 18888 --no-browser --allow-root --NotebookApp.token '' \
        --FileContentsManager.checkpoints_dir=/qsfs2/jupyter_data/.checkpoints  \
        --notebook-dir /qsfs2 --NotebookApp.base_url=/jupyter/ 
 
elif [[ "$1" == "jupyterroot" ]]; then
    exec jupyter notebook  --ip 0.0.0.0 --port 18889 --no-browser --allow-root --NotebookApp.token '' \
        --FileContentsManager.checkpoints_dir=/qsfs2/jupyter_data/.checkpoints \
        --notebook-dir /qsfs2 --NotebookApp.base_url=/jupyterroot/ 
elif [ $# -eq 0 ]; then
        exec bash -c "echo executing  ;\
         while true ; \
         do  \
            sleep 10000 ;\
         done ;"
else
    # Execute the provided command
    echo "executing $@"
    exec "$@"
fi
notebook_dir 