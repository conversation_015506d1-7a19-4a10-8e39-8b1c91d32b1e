services:
  jupyter:
    image: qs:latest
    container_name: jupyter
    restart: always
    network_mode: "host" # Use the host network
    volumes:
      - /qsfs2:/qsfs2 # Bind your nginx.conf
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    command: jupyter
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
    ports:
      - "8888:8888"
      - "8889:8889"
    environment:
      - HOST_UID=${HOST_UID}
      - HOST_GID=${HOST_GID}

  shell:
    image: qs:latest
    container_name: shell
    restart: always
    network_mode: "host" # Use the host network
    volumes:
      - /qsfs2:/qsfs2 # Bind your nginx.conf
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
    ports:
      - "8888:8888"
      - "8889:8889"
    environment:
      - HOST_UID=${HOST_UID}
      - HOST_GID=${HOST_GID}

  jupyterroot:
    image: qs:latest
    container_name: jupyterroot
    restart: always
    network_mode: "host" # Use the host network
    volumes:
      - /qsfs2:/qsfs2 # Bind your nginx.conf
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    command: jupyterroot
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
    ports:
      - "8888:8889"
      - "8889:8889"
    environment:
      - HOST_UID=${HOST_UID}
      - HOST_GID=${HOST_GID}
