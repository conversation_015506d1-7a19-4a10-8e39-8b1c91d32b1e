#!/bin/bash
set -e
# Start Jupyter if the container is run with 'jupyter' argument

# Create a user to match the host user
echo "Running with $@ +++++++++++++++++++++++++++  ${HOST_UID}  ${HOST_GID}"
HOST_UID=${HOST_UID:-2720087}
HOST_GID=${HOST_GID:-2720087}
HOST_USERNAME=qsuser

echo "Running with $(id -u qsuser) : $(id -g qsuser)"

if [ "$HOST_UID" != "$(id -u qsuser)" ] || [ "$HOST_GID" != "$(id -g qsuser)" ]; then
    echo "Creating user $HOST_USERNAME with UID $HOST_UID and GID $HOST_GID"
    groupmod -g $HOST_GID qsuser
    usermod -u $HOST_UID -g $HOST_GID qsuser
    echo "Modified user $HOST_USERNAME with UID $HOST_UID and GID $HOST_GID"
    chown -R $HOST_UID:$HOST_GID /home/<USER>
else 
    echo "User already exists with UID $HOST_UID and GID $HOST_GID"
fi

exec gosu $HOST_USERNAME bash /usr/local/bin/jupyter_entrypoint.sh "$@"
