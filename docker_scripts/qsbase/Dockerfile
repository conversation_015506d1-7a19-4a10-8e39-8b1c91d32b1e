# Base image with Ubuntu 22.04 and CUDA 12.4
FROM nvidia/cuda:12.4.0-devel-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    PATH="/opt/conda/bin:$PATH" \
    CONDA_DIR=/opt/conda


# Install system dependencies and Miniconda

RUN apt-get update && apt-get install -y --no-install-recommends \
    wget \
    curl \
    git \
    ca-certificates \
    build-essential \
    libssl-dev \
    libffi-dev \
    openssh-server \
    bash \
    telnet \
    gosu \
    x2goserver \
    x2goserver-xsession \
    libglu1-mesa \
    dbus-x11 \
    software-properties-common \
    x11vnc \
    rclone \
    fuse3 \
    python3-tk \
    zip \
    python3 \
    python3-pip 
# && wget --quiet https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh \
# && bash /tmp/miniconda.sh -b -p /opt/conda \
# && rm /tmp/miniconda.sh \
# && /opt/conda/bin/conda update -n base -c defaults conda -y

#&& rm -rf /var/lib/apt/lists/* \
RUN groupadd -g 2720087 qsuser

RUN useradd -ms /bin/bash qsuser  -u 2720087 -g 2720087 && usermod -aG sudo qsuser 
#RUN chown -R qsuser:qsuser $CONDA_DIR
RUN echo "umask 0000" >> /home/<USER>/.bashrc

RUN   mkdir -p /work && chmod 777 /work 
#&& chown -R qsuser:qsuser /work

RUN mkdir /qsfs2 && chmod 777 /qsfs2 
#&& chown -R qsuser:qsuser /qsfs2

USER qsuser
ENV UMASK 0000

# Add Conda to PATH
ENV PATH="/opt/conda/bin:/home/<USER>/.local/bin:$PATH"
ENV PATH="/opt/conda/envs/qs/bin:$PATH"



# Initialize Conda and create the environment
# RUN conda init bash && \
#     $CONDA_DIR/bin/conda create -y -n qs python=3.12

# # Modify the .bashrc to always activate the qs environment for the qsuser user
# RUN echo "source $CONDA_DIR/bin/activate qs" >> /home/<USER>/.bashrc

# RUN $CONDA_DIR/bin/conda run -n qs pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124
# RUN $CONDA_DIR/bin/conda run -n qs  pip install jupyter psutil numpy pandas matplotlib 
RUN umask 0000 && pip3 install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124
RUN  umask 0000 && pip3 install jupyter psutil numpy pandas matplotlib 

# RUN echo "\
#     conda activate qs" >> ~/.bashrc

USER root

# Add entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

RUN chown  qsuser:qsuser /usr/local/bin/entrypoint.sh

COPY jupyter_entrypoint.sh /usr/local/bin/jupyter_entrypoint.sh
RUN chmod +x /usr/local/bin/jupyter_entrypoint.sh

RUN chown qsuser:qsuser /usr/local/bin/jupyter_entrypoint.sh
#
# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# Expose Jupyter default port
EXPOSE 18888 18889 18890 18891 18892 18893 18894 18895 18896 18897 18898 18900 

