#!/bin/bash

# Directory to scan for media files
SCAN_DIR="/data"

# Loop through video files and generate thumbnails
find "$SCAN_DIR" -type f \( -iname '*.mp4' -o -iname '*.mkv' -o -iname '*.avi' \) -exec bash -c '
for video; do
    thumbnail_dir="$(dirname "$video")/.thumbnails"
    mkdir -p "$thumbnail_dir"
    thumbnail_file="$thumbnail_dir/$(basename "$video").png"
    if [ ! -f "$thumbnail_file" ]; then
        echo "Generating thumbnail for $video..."
        ffmpegthumbnailer -f -i "$video" -o "$thumbnail_file"
    fi
done
' bash {} +

echo "Thumbnail generation complete."
echo "Starting filebrowser..."
filebrowser  -b /filebrowser --noauth --root=/data --address=0.0.0.0 -p 8080 --database=/config/filebrowser.db -c=/config