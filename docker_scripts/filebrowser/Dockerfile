# Use Ubuntu as the base image
FROM ubuntu:22.04

# Update and install dependencies
RUN apt update && apt install -y \
    software-properties-common \
    wget \
    ffmpeg \
    bash \
    totem \
    curl \
    ffmpegthumbnailer \
    && rm -rf /var/lib/apt/lists/*

# Download and install FileBrowser
RUN wget  https://raw.githubusercontent.com/filebrowser/get/master/get.sh  -O /tmp/get.sh && bash /tmp/get.sh 

# Copy the thumbnail generation script into the container
COPY generate_thumbnails.sh /usr/local/bin/generate_thumbnails.sh
RUN chmod +x /usr/local/bin/generate_thumbnails.sh

# Expose the default FileBrowser port
EXPOSE 8080

# Entrypoint to generate thumbnails and start FileBrowser
ENTRYPOINT ["/bin/bash", "-c", "/usr/local/bin/generate_thumbnails.sh"]
