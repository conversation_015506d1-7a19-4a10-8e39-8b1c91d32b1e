#commands prompts etc
# command for getting all the users of perticular group on AD
az ad group member list --group qsusergroup001 --output json | jq -r '.[] | "\(.userPrincipalName // "null"):\(.displayName // "null"):\(.id // "null")"'

#dns alia creation
az network dns record-set cname set-record --resource-group qsuser --zone-name onazure.quantstac.com --record-set-name qst4016 --cname qst4spot016PublicIP.cloudapp.azure.com


# ssh from ms.admin

sudo -i -H -u <EMAIL> bash
-------------------------------------------

I have nginx along with couple of services setup, it is working correctly
following is the file structure

proxy
   - nginx
      - Dockerfile
      - nginx.conf
      - certs
       - cert.pem
       - chain.pem
       - fullchain.pem
       - privkey.pem
       - server.key
    
   - docker-compose.yml

Dockerfile 
-----------
FROM nginx:latest 
RUN mkdir -p /etc/nginx/
RUN mkdir -p /etc/nginx/certs/

nginx.conf
---------
events { }

http {
    # HTTP Configuration
    server {
        listen 18890;
        server_name qst4spot012.onazure.quantstac.com;

        # Proxy for /comfy
        location /comfy/ {
            proxy_pass http://127.0.0.1:18892/; # Forward to backend
            rewrite ^/comfy(/.*)$ $1 break;   # Remove /comfy prefix
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Proxy for /jupyter
        location /jupyter/ {
            proxy_pass http://127.0.0.1:18888/; # Forward to backend
            rewrite ^/jupyter(/.*)$ $1 break; # Remove /jupyter prefix
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # HTTPS Configuration
    server {
        listen 18891 ssl;
        server_name qst4spot012.onazure.quantstac.com;

        # SSL Certificate Configuration
        ssl_certificate /etc/nginx/certs/fullchain.pem;
        ssl_certificate_key /etc/nginx/certs/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;

        # Proxy for /comfy
        location /comfy/ {
            proxy_pass http://127.0.0.1:18892/;
            rewrite ^/comfy(/.*)$ $1 break;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Proxy for /jupyter
        location /jupyter/ {
            proxy_pass http://127.0.0.1:18888/;
            rewrite ^/jupyter(/.*)$ $1 break;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}

docker-compose.yml
-----------------
version: '3.9'

services:
  nginx_reverse_proxy:
   #  image: qsnginx:latest
   build: ./nginx/
    container_name: nginx_reverse_proxy
    restart: always
    network_mode: "host" # Use the host network
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro  # Bind your nginx.conf
      - ./nginx/certs:/etc/nginx/certs:ro           # Mount the directory containing certificates
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"


I want to add  Azure AD authentication using oauth2-proxy. Goal is to allow only valid autherized Azure AD users to be able to acess the service 

Give detailed steps to setup, and validation steps

secret val **************************************** 
secret id d1acc881-4b36-49b8-bec4-16c823afeebb
client id 99cc76a2-0c88-4a70-9e0f-08377787e8e7
tenant id 73b55c4d-0679-4a00-acbf-3ef8759d1c31


https://qst4spot012.onazure.quantstac.com:18890/comfy/


---- second prompt

I have nginx along with couple of services setup, I oauthentication setup via oauth2-proxy to Azure AD it is working correctly
following is the file structure.  It works well for service comfy but not working for jupyter

proxy
   - nginx
      - Dockerfile
      - nginx.conf
      - certs
       - cert.pem
       - chain.pem
       - fullchain.pem
       - privkey.pem
       - server.key
    
   - docker-compose.yml

Dockerfile 
-----------
FROM nginx:latest 
RUN mkdir -p /etc/nginx/
RUN mkdir -p /etc/nginx/certs/

events { }

http {
    # HTTP Configuration
        proxy_buffer_size          16k;
        proxy_buffers              4 32k;
        proxy_busy_buffers_size    64k;

        server {
            listen 18890 ssl;
            server_name qst4spot012.onazure.quantstac.com;

            ssl_certificate /etc/nginx/certs/fullchain.pem;
            ssl_certificate_key /etc/nginx/certs/privkey.pem;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_prefer_server_ciphers on;

            # OAuth2 Proxy Configuration
            location /oauth2/ {
                proxy_pass http://127.0.0.1:4180;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Authentication Validation
            location /oauth2/auth {
                proxy_pass http://127.0.0.1:4180;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                internal;
            }

            # Proxy for /comfy/
            location /comfy/ {
                auth_request /oauth2/auth;
                error_page 401 = /oauth2/sign_in?rd=$request_uri;

                proxy_pass http://127.0.0.1:18892/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Proxy for /jupyter/
            location /jupyter/ {
                auth_request /oauth2/auth;
                error_page 401 = /oauth2/sign_in?rd=$request_uri;

                proxy_pass http://127.0.0.1:18888/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            
        }
}

docker-compose.yml
-----------------
services:
  nginx_reverse_proxy:
    image: nginx:latest
   # build: ./nginx/
    container_name: nginx_reverse_proxy
    restart: always
    network_mode: "host" # Use the host network
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro  # Bind your nginx.conf
      - ./nginx/certs:/etc/nginx/certs:ro           # Mount the directory containing certificates
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  oauth2_proxy:
    image: quay.io/oauth2-proxy/oauth2-proxy:v7.4.0
    container_name: oauth2_proxy
    restart: always
    environment:
      OAUTH2_PROXY_PROVIDER: "azure"
      OAUTH2_PROXY_CLIENT_ID: "99cc76a2-0c88-4a70-9e0f-08377787e8e7"
      OAUTH2_PROXY_CLIENT_SECRET: "****************************************"
      OAUTH2_PROXY_COOKIE_SECRET: "8hEOo5iCoC5yWiXczbUX0SkdFEBFuvIvCDVNhm2DzlI="
      OAUTH2_PROXY_COOKIE_SECURE: "true"
      OAUTH2_PROXY_EMAIL_DOMAINS: "*"
      OAUTH2_PROXY_HTTP_ADDRESS: "0.0.0.0:4180"
      OAUTH2_PROXY_REDIRECT_URL: "https://qst4spot012.onazure.quantstac.com:18890/oauth2/callback"
      OAUTH2_PROXY_AZURE_TENANT: "73b55c4d-0679-4a00-acbf-3ef8759d1c31"
      OAUTH2_PROXY_OIDC_ISSUER_URL: "https://login.microsoftonline.com/73b55c4d-0679-4a00-acbf-3ef8759d1c31/v2.0"
      OAUTH2_PROXY_REVERSE_PROXY: "true"
      OAUTH2_PROXY_PASS_ACCESS_TOKEN: "true"
      OAUTH2_PROXY_SKIP_PROVIDER_BUTTON: "true"
      OAUTH2_PROXY_DEFAULT_ALLOWED_GROUPS: "true"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  
    ports:
      - "4180:4180"
    network_mode: "host"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

It is working well with /comfy/ service. However for /jupyter/service it is not working properly.  
The jupyter does rewrite url many times, when it rewrites address.  /jupyter/ is not added back by nginx/oauth2-proxy, creating issue an issue

e.g. 

https://qst4spot012.onazure.quantstac.com:18890/jupyter/

after hitting jupyter service it redirectors to /tree?
the resulting url 
is 
https://qst4spot012.onazure.quantstac.com:18890/tree?

correct url should be 
https://qst4spot012.onazure.quantstac.com:18890/jupyter/tree?  

If I add manually /jupyter/ in browser at next step again same issue for next redirection

Please do required correction and steps to verify This


--------------------------------------
I have jupyter installed in a conda env as below  with following requirements.txt 
jupyter
torch
torchsde
torchvision
einops
transformers>=4.25.1
safetensors>=0.3.0
aiohttp
pyyaml
Pillow
scipy
tqdm
psutil
kornia>=0.7.1
numpy
pandas
matplotlib

It runs on port 18888 with following command 
jupyter notebook --NotebookApp.token QsJupyter@123 --FileContentsManager.checkpoints_dir=/qsfs2/services/jupyter/.checkpoints --no-browser --port $PORT --ip 0.0.0.0  --notebook-dir /qsfs2

It is accessible via url 
http://qst4spot012.onazure.quantstac.com:18888
and is working well. 

Now I have added nginx for reverse proxy, and authencation by Azure AD via oauth2-proxy, for 2 services on same machine,  /comfy and /jupyter 
/comfy is working well  
Here is nginx.conf 

events { }

http {
    # HTTP Configuration
        proxy_buffer_size          16k;
        proxy_buffers              4 32k;
        proxy_busy_buffers_size    64k;

        server {
            listen 18890 ssl;
            server_name qst4spot012.onazure.quantstac.com;

            ssl_certificate /etc/nginx/certs/fullchain.pem;
            ssl_certificate_key /etc/nginx/certs/privkey.pem;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_prefer_server_ciphers on;

            # OAuth2 Proxy Configuration
            location /oauth2/ {
                proxy_pass http://127.0.0.1:4180;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Authentication Validation
            location /oauth2/auth {
                proxy_pass http://127.0.0.1:4180;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                internal;
            }

            # Proxy for /comfy/
            location /comfy/ {
                auth_request /oauth2/auth;
                error_page 401 = /oauth2/sign_in?rd=$request_uri;

                proxy_pass http://127.0.0.1:18892/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Proxy for /jupyter/
            location /jupyter/ {
                auth_request /oauth2/auth;
                error_page 401 = /oauth2/sign_in?rd=$request_uri;

                proxy_pass http://127.0.0.1:18888/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
        }
} 

While service comfy accessed via  url 
https://qst4spot012.onazure.quantstac.com:18890/comfy/  works well, for jupyter 

https://qst4spot012.onazure.quantstac.com:18890/jupyter/ redirects authenication is done done but reverse url  jupyter rewrites it the rewritten url is missing /jupyter e.g. 
the url comes back as 
https://qst4spot012.onazure.quantstac.com:18890/tree?  the correct should have been 
https://qst4spot012.onazure.quantstac.com:18890/jupyter/tree? 
If I manually corrects it goes ahead, again issue next rewritten url once I correct that. jupyter works however some css styles are missed 

Give the solution for getting correct url back and correcting css styles as well


The docker-compose.yml file that starts nginx and oauth2-proxy 
nginx.conf - config for nginx
oauth2-proxy.cfg - config for oauth2-proxy
proxy.env - environment for oauth2-proxy environment variables

nginx does reverse proxy for 3 services,  each of them has an app called qsComfy (started outside of docker-compose )
at present nginx takes care of ssl termination, Authentication via oauth2-proxy.
oauth2-proxy does authencation users using azure enter provider.  
Currently it allows all the authenticated entra users. 

I want to modify it to restrict users to each nginx service either via group or roles or other ways,  so that different users have access different set of services also some users may not have access to any service
suggest various option discuss in detail pros and cons, so that we can choose 1 and implement
