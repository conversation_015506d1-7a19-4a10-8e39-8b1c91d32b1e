events { }

error_log /var/log/nginx/error.log debug;

http {
    client_max_body_size 0;
    proxy_buffer_size          16k;
    proxy_buffers              4 32k;
    proxy_busy_buffers_size    64k;

    #developer mode 18893 & 28893 jupyter /jupyter 28888 
    server {
        listen 18892 ssl;
        # server_name qst4spot012.onazure.quantstac.com;

        ssl_certificate /etc/nginx/certs/fullchain.pem;
        ssl_certificate_key /etc/nginx/certs/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;

        # OAuth2 Proxy endpoints
        location /oauth2/ {
            proxy_pass http://127.0.0.1:4180;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 0;
        }

        location /oauth2/auth {
            proxy_pass http://127.0.0.1:4180;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 0;
            internal;
        }

        location /debug {
            return 200 "Roles: $http_x_auth_request_roles\nUser: $http_x_auth_request_email\n";
        }

        # Jupyter proxy
        location /jupyter/ {
            auth_request /oauth2/auth;
            error_page 401 = /oauth2/sign_in?rd=$request_uri;

            proxy_pass http://127.0.0.1:28888$request_uri;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Scheme $scheme;

            proxy_buffering off;
            # add_header Access-Control-Allow-Origin https://qst4spot010.onazure.quantstac.com:18890;
            add_header Access-Control-Allow-Credentials true;
            client_max_body_size 0;
        }

        location / {
            auth_request /oauth2/auth; 
            error_page 401 = /oauth2/sign_in?rd=$request_uri;


            # Get group information from OAuth2 Proxy
            # auth_request_set $auth_groups $upstream_http_x_auth_groups;
            auth_request_set $auth_roles $upstream_http_x_auth_request_roles;


        #    # Check if user is in the developer group
        #     if ($auth_roles  !~ "4b937d2e-3acf-40a4-a6b7-4177f647b0b7") {
        #         return 403 "Access denied: You need developer permissions";
        #     }

            # Ensure Authorization header is passed
            auth_request_set $auth_token $upstream_http_x_auth_request_access_token;
            proxy_set_header X-Auth-Token $auth_token;
            
            # Pass user info
            auth_request_set $auth_user $upstream_http_x_auth_request_user;
            proxy_set_header X-Auth-User $auth_user;
            
            auth_request_set $auth_email $upstream_http_x_auth_request_email;
            proxy_set_header X-Auth-Email $auth_email;

            # Pass original host information
            proxy_set_header X-Original-Host $host;
            proxy_set_header X-Original-Port $server_port;
           
            # Pass cookies for session maintenance
            proxy_set_header Cookie $http_cookie;
            
            # Pass authorization header if present
            proxy_set_header Authorization $http_authorization;
            proxy_pass_header Authorization;
    
            # Fix trailing slash and ensure proper content type handling
            proxy_pass http://127.0.0.1:28892;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Content-Type $http_content_type;

            # Add CORS headers
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
            add_header 'X-Debug-Groups' '$auth_groups';
            add_header 'X-Debug-Roles' '$auth_roles';


            add_header X-Debug-Roles $auth_roles;
            proxy_buffering off;
            client_max_body_size 0;

            # Handle OPTIONS requests for CORS preflight
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }
    }

    #ui client 18890 28890 filebrowser /filebrowser 28898
    server {
        listen 18890 ssl;
     
        ssl_certificate /etc/nginx/certs/fullchain.pem;
        ssl_certificate_key /etc/nginx/certs/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;

        # OAuth2 Proxy endpoints
        location  /oauth2/ {
            proxy_pass http://127.0.0.1:4180;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 0;
        }

        location /oauth2/auth {
            proxy_pass http://127.0.0.1:4180;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 0;
            internal;
        }

        # filebrowser proxy
        location /filebrowser/ {
            auth_request /oauth2/auth;
            error_page 401 = /oauth2/sign_in?rd=$request_uri;

            proxy_pass http://127.0.0.1:28898;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Scheme $scheme;

            proxy_buffering off;
            add_header Access-Control-Allow-Credentials true;
            client_max_body_size 0;
        }


        location / {

            auth_request /oauth2/auth; 
            error_page 401 = /oauth2/sign_in?rd=$request_uri;

            # Get group information from OAuth2 Proxy
            auth_request_set $auth_groups $upstream_http_x_auth_groups;
            
            # # Check if user is in the operator group
            # if ($auth_groups !~ "b1200484-9342-4caf-8df9-7d9c3f835585") {
            #     return 403 "Access denied: You need operator permissions";
            # }

            # Pass OAuth2 token headers to the backend
            auth_request_set $auth_token $upstream_http_x_auth_request_access_token;
            proxy_set_header X-Auth-Token $auth_token;
            
            # Pass user info
            auth_request_set $auth_user $upstream_http_x_auth_request_user;
            proxy_set_header X-Auth-User $auth_user;
            
            auth_request_set $auth_email $upstream_http_x_auth_request_email;
            proxy_set_header X-Auth-Email $auth_email;

             # Pass original host information
            proxy_set_header X-Original-Host $host;
            proxy_set_header X-Original-Port $server_port;
           
           # Pass cookies for session maintenance
            proxy_set_header Cookie $http_cookie;
            
            # Pass authorization header if present
            proxy_set_header Authorization $http_authorization;
            proxy_pass_header Authorization;

            proxy_pass http://127.0.0.1:28890$request_uri; # Preserve encoded URI
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Debug-Request-URI $request_uri; # Add a debug header
            proxy_http_version 1.1;
            proxy_set_header X-Scheme $scheme;
    
            proxy_buffering off;
                # Allow CORS headers
            # add_header Access-Control-Allow-Origin https://qst4spot012.onazure.quantstac.com:18890;
            add_header Access-Control-Allow-Credentials true;

            # WebSocket headers
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # Increase client body size limit
            client_max_body_size 0;
        }
    }

    # api server 18891  28891
    server {
        listen 18891 ssl;
     
        ssl_certificate /etc/nginx/certs/fullchain.pem;
        ssl_certificate_key /etc/nginx/certs/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;

        # OAuth2 Proxy endpoints
        location  /oauth2/ {
            proxy_pass http://127.0.0.1:4180;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 0;
        }

        location /oauth2/auth {
            proxy_pass http://127.0.0.1:4180;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 0;
            internal;
        }


        location / {

            auth_request /oauth2/auth; 
            error_page 401 = /oauth2/sign_in?rd=$request_uri;

            # Get group information from OAuth2 Proxy
            auth_request_set $auth_groups $upstream_http_x_auth_groups;

            # Check if user is in the operator group
            # if ($auth_groups !~ "b1200484-9342-4caf-8df9-7d9c3f835585") {
            #     return 403 "Access denied: You need operator permissions";
            # }

            # Check if user is in the developer group
            # if ($auth_groups !~ "4b937d2e-3acf-40a4-a6b7-4177f647b0b7") {
            #     return 403 "Access denied: You need operator permissions";
            # }

          
            # Pass OAuth2 token headers to the backend
            auth_request_set $auth_token $upstream_http_x_auth_request_access_token;
            proxy_set_header X-Auth-Token $auth_token;
            
            # Pass user info
            auth_request_set $auth_user $upstream_http_x_auth_request_user;
            proxy_set_header X-Auth-User $auth_user;
            
            auth_request_set $auth_email $upstream_http_x_auth_request_email;
            proxy_set_header X-Auth-Email $auth_email;

             # Pass original host information
            proxy_set_header X-Original-Host $host;
            proxy_set_header X-Original-Port $server_port;
           
           # Pass cookies for session maintenance
            proxy_set_header Cookie $http_cookie;
            
            # Pass authorization header if present
            proxy_set_header Authorization $http_authorization;
            proxy_pass_header Authorization;

            proxy_pass http://127.0.0.1:28891$request_uri; # Preserve encoded URI
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Debug-Request-URI $request_uri; # Add a debug header
            proxy_http_version 1.1;
            proxy_set_header X-Scheme $scheme;
    
            proxy_buffering off;
                # Allow CORS headers
            # add_header Access-Control-Allow-Origin https://qst4spot012.onazure.quantstac.com:18890;
            add_header Access-Control-Allow-Credentials true;

            # WebSocket headers
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # Increase client body size limit
            client_max_body_size 0;
        }
    }
    #local confyui without docker
    server {
        listen 18893 ssl;
        # server_name qst4spot012.onazure.quantstac.com;

        ssl_certificate /etc/nginx/certs/fullchain.pem;
        ssl_certificate_key /etc/nginx/certs/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;

        # OAuth2 Proxy endpoints
        location /oauth2/ {
            proxy_pass http://127.0.0.1:4180;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 0;
        }

        location /oauth2/auth {
            proxy_pass http://127.0.0.1:4180;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 0;
            internal;
        }

        location /debug {
            return 200 "Roles: $http_x_auth_request_roles\nUser: $http_x_auth_request_email\n";
        }

        location / {
            auth_request /oauth2/auth; 
            error_page 401 = /oauth2/sign_in?rd=$request_uri;


            # Get group information from OAuth2 Proxy
            # auth_request_set $auth_groups $upstream_http_x_auth_groups;
            auth_request_set $auth_roles $upstream_http_x_auth_request_roles;


        #    # Check if user is in the developer group
        #     if ($auth_roles  !~ "4b937d2e-3acf-40a4-a6b7-4177f647b0b7") {
        #         return 403 "Access denied: You need developer permissions";
        #     }

            # Ensure Authorization header is passed
            auth_request_set $auth_token $upstream_http_x_auth_request_access_token;
            proxy_set_header X-Auth-Token $auth_token;
            
            # Pass user info
            auth_request_set $auth_user $upstream_http_x_auth_request_user;
            proxy_set_header X-Auth-User $auth_user;
            
            auth_request_set $auth_email $upstream_http_x_auth_request_email;
            proxy_set_header X-Auth-Email $auth_email;

            # Pass original host information
            proxy_set_header X-Original-Host $host;
            proxy_set_header X-Original-Port $server_port;
           
            # Pass cookies for session maintenance
            proxy_set_header Cookie $http_cookie;
            
            # Pass authorization header if present
            proxy_set_header Authorization $http_authorization;
            proxy_pass_header Authorization;
    
            # Fix trailing slash and ensure proper content type handling
            proxy_pass http://127.0.0.1:28893$request_uri;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Content-Type $http_content_type;

            # Add CORS headers
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
            add_header 'X-Debug-Groups' '$auth_groups';
            add_header 'X-Debug-Roles' '$auth_roles';


            add_header X-Debug-Roles $auth_roles;
            proxy_buffering off;
            client_max_body_size 0;

            # Handle OPTIONS requests for CORS preflight
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }
    }


}
