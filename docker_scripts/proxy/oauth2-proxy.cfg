provider = "azure"
client_id = "99cc76a2-0c88-4a70-9e0f-08377787e8e7"
client_secret = "****************************************"
cookie_secret = "8hEOo5iCoC5yWiXczbUX0SkdFEBFuvIvCDVNhm2DzlI="
cookie_secure = true
email_domains = "*"
http_address = "0.0.0.0:4180"
redirect_url = "${OAUTH2_PROXY_REDIRECT_URL}"
azure_tenant = "73b55c4d-0679-4a00-acbf-3ef8759d1c31"
oidc_issuer_url = "https://login.microsoftonline.com/73b55c4d-0679-4a00-acbf-3ef8759d1c31/v2.0"
reverse_proxy = true
pass_access_token = true
skip_provider_button = true
set_xauthrequest = true
pass_user_headers = true
whitelist_domains = "${OAUTH2_PROXY_WHITELIST_DOMAINS}"
scope = "openid profile email groups"
# Add these lines for group-based authorization
# allowed_groups = ["4b937d2e-3acf-40a4-a6b7-4177f647b0b7","b1200484-9342-4caf-8df9-7d9c3f835585"]
# azure_groups_claim = "X-Auth-Groups"

# Required to pass group info
set_authorization_header = true
pass_authorization_header = true
