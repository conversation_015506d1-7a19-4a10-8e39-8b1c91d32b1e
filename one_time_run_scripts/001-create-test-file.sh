#!/bin/bash
#USER=<EMAIL>
#USE_PROFILE=false
#REBOOT=false

# Variables
TEST_FILE="/home/<USER>/test_file.txt"
LOG_FILE="/qsfs2/one_time_script_logs/$VM_NAME/sample_script.log"
SUCCESS=true

# Create a test file and write a message
if echo "This is a test file created by the sample one-time script on VM $VM_NAME." > "$TEST_FILE"; then
    echo "$(date): Created test file at $TEST_FILE" >> "$LOG_FILE"
else
    SUCCESS=false
    echo "$(date): Failed to create test file at $TEST_FILE" >> "$LOG_FILE"
fi

# Exit with status
if [ "$SUCCESS" = true ]; then
    exit 0
else
    exit 1
fi


