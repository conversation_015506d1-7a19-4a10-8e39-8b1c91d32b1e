version: '3'
services:
  qscomfy:
    image: qsacr001.azurecr.io/qs-comfy:latest
    environment:
      - QSCOMFY_ARGS=--use-pytorch-cross-attention --enable-cors-header --multi-user
    volumes:
      - ./:/qsComfy
      # - ./custom_nodes:/qsComfy/custom_nodes
      # - ./models:/qsComfy/models
      # - ./input:/qsComfy/input
      # - ./output:/qsComfy/output
      # - ./artlet:/qsComfy/artlet
      # - ./qs_custom_nodes:/qsComfy/qs_custom_nodes
   
    logging:
      driver: "json-file"
      options:
        labels: "com.example.logging"
        env: "os,customer"
      
    network_mode: "host"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
              
