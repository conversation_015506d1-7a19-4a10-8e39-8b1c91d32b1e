# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
# created-by: conda 24.9.2
_libgcc_mutex=0.1=main
_openmp_mutex=5.1=1_gnu
aiohappyeyeballs=2.4.4=pypi_0
aiohttp=3.11.10=pypi_0
aiosignal=1.3.1=pypi_0
attrs=24.2.0=pypi_0
bzip2=1.0.8=h5eee18b_6
ca-certificates=2024.11.26=h06a4308_0
certifi=2024.8.30=pypi_0
cffi=1.17.1=pypi_0
charset-normalizer=3.4.0=pypi_0
einops=0.8.0=pypi_0
expat=2.6.3=h6a678d5_0
frozenlist=1.5.0=pypi_0
idna=3.10=pypi_0
jinja2=3.1.4=pypi_0
kornia=0.7.4=pypi_0
kornia-rs=0.1.7=pypi_0
ld_impl_linux-64=2.40=h12ee557_0
libffi=3.4.4=h6a678d5_1
libgcc-ng=11.2.0=h1234567_1
libgomp=11.2.0=h1234567_1
libstdcxx-ng=11.2.0=h1234567_1
libuuid=1.41.5=h5eee18b_0
markupsafe=3.0.2=pypi_0
mpmath=1.3.0=pypi_0
multidict=6.1.0=pypi_0
ncurses=6.4=h6a678d5_0
networkx=3.4.2=pypi_0
numpy=2.2.0=pypi_0
nvidia-cublas-cu12=********=pypi_0
nvidia-cuda-cupti-cu12=12.4.127=pypi_0
nvidia-cuda-nvrtc-cu12=12.4.127=pypi_0
nvidia-cuda-runtime-cu12=12.4.127=pypi_0
nvidia-cudnn-cu12=********=pypi_0
nvidia-cufft-cu12=********=pypi_0
nvidia-curand-cu12=**********=pypi_0
nvidia-cusolver-cu12=********=pypi_0
nvidia-cusparse-cu12=**********=pypi_0
nvidia-nccl-cu12=2.21.5=pypi_0
nvidia-nvjitlink-cu12=12.4.127=pypi_0
nvidia-nvtx-cu12=12.4.127=pypi_0
openssl=3.0.15=h5eee18b_0
packaging=24.2=pypi_0
pillow=11.0.0=pypi_0
pip=24.2=py312h06a4308_0
propcache=0.2.1=pypi_0
psutil=6.1.0=pypi_0
pycparser=2.22=pypi_0
python=3.12.7=h5148396_0
readline=8.2=h5eee18b_0
regex=2024.11.6=pypi_0
requests=2.32.3=pypi_0
safetensors=0.4.5=pypi_0
scipy=1.14.1=pypi_0
sentencepiece=0.2.0=pypi_0
setuptools=75.1.0=py312h06a4308_0
soundfile=0.12.1=pypi_0
spandrel=0.4.0=pypi_0
sqlite=3.45.3=h5eee18b_0
sympy=1.13.1=pypi_0
tk=8.6.14=h39e8969_0
tokenizers=0.21.0=pypi_0
torch=2.5.1+cu124=pypi_0
torchaudio=2.5.1+cu124=pypi_0
torchsde=0.2.6=pypi_0
torchvision=0.20.1+cu124=pypi_0
tqdm=4.67.1=pypi_0
trampoline=0.1.2=pypi_0
transformers=4.47.0=pypi_0
triton=3.1.0=pypi_0
tzdata=2024b=h04d1e81_0
urllib3=2.2.3=pypi_0
wheel=0.44.0=py312h06a4308_0
xformers=0.0.28.post3=pypi_0
xz=5.4.6=h5eee18b_1
yarl=1.18.3=pypi_0
zlib=1.2.13=h5eee18b_1
