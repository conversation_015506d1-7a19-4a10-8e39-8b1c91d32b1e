{"3": {"inputs": {"seed": 386855264697443, "steps": 30, "cfg": 8.0, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "dreamshaper_15.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": ["52", 1], "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "text, watermark", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "41": {"inputs": {"string": "beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"}, "class_type": "StringConstant", "_meta": {"title": "String Constant"}}, "42": {"inputs": {"output": "", "source": ["52", 0]}, "class_type": "Display Any (rgthree)", "_meta": {"title": "Display Any (rgthree)"}}, "43": {"inputs": {"output": "", "source": ["52", 0]}, "class_type": "Display Any (rgthree)", "_meta": {"title": "Display Any (rgthree)"}}, "47": {"inputs": {"name_0": "khk", "name_1": "kkj", "name_2": "string", "name_3": "", "name_4": "", "name_5": "", "name_6": "", "name_7": "", "name_8": "", "name_9": "", "output_0": ["8", 0], "output_1": ["8", 0], "output_2": ["50", 2]}, "class_type": "QSOutput", "_meta": {"title": "QS Output"}}, "48": {"inputs": {"output": "", "source": ["47", 0]}, "class_type": "Display Any (rgthree)", "_meta": {"title": "Display Any (rgthree)"}}, "49": {"inputs": {"images": ["8", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "50": {"inputs": {"number_type": "integer", "number": 0.0}, "class_type": "Constant Number", "_meta": {"title": "Constant Number"}}, "52": {"inputs": {"name_0": "", "name_1": "", "name_2": "", "name_3": "", "name_4": "", "name_5": "", "name_6": "", "name_7": "", "name_8": "", "name_9": "", "input_0": ["41", 0]}, "class_type": "QSLetInputs", "_meta": {"title": "QS LetInputs"}}, "53": {"inputs": {"output": "", "source": ["52", 1]}, "class_type": "Display Any (rgthree)", "_meta": {"title": "Display Any (rgthree)"}}, "55": {"inputs": {"save_prompt_api": "true", "output_path": "./qslets", "filename_prefix": "qslet1", "filename_delimiter": "_", "filename_number_padding": 2, "parse_text_tokens": false}, "class_type": "Export API", "_meta": {"title": "Export API"}}}