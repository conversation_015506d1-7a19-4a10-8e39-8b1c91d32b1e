## You can clone on ubuntus as well as on remote machine after create

# All the scripts are written for t4 spot machines

# machine name used is qsA1spot090

# If spot is not required change in file vm_create.sh

1. login in to machine with

az ssh vm --ip //$$@@@qsA1spot090.onazure.quantstac.com

2. git clone repo & run setup commands

git clone https://<EMAIL>/quantstac/infra.git
cd infra/vm_setup
git pull
sudo su
bash ./vm_init.sh //$$@@@qsA1spot090

3.  If all well reboot the machine

reboot

#Old conda install
#conda install -y pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia
#old custom_nodes install

# cd custom_nodes

# find . -name "requirements.txt" -exec pip install -r {} \;

# Ollam installs

#echo "+++Installing Ollam+++"
#wget https://ollama.com/install.sh
#bash ./install.sh
#ollama run mistral
#now install jupyter
#cd /qsfs2/services/infra/jupyter/
#pip install --upgrade-r frozen_requirements.txt

Add an ACR role

az role assignment create \
  --assignee-object-id 8f84cbf6-ca4c-4e76-873a-9a09e087f6bb \
  --role acrpull \
  --scope /subscriptions/2891bfbb-6705-490c-a09f-3fc8ff555228/resourceGroups/qsuser/providers/Microsoft.ContainerRegistry/registries/qsacr001

sudo -H -i -u <EMAIL> bash

to check reboot cron
sudo rm /var/run/crond.reboot 

sudo service cron restart


Cron logs
grep CRON /var/log/syslog
