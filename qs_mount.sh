#!/bin/bash
# Default values
DEFAULT_SOURCE_DIR=./qs_custom_nodes/
DEFAULT_TARGET_DIR="./custom_nodes"

# Read arguments
SOURCE_DIR="${1:-$DEFAULT_SOURCE_DIR}"
TARGET_DIR="${2:-$DEFAULT_TARGET_DIR}"
NODE_DIR="${3:-ALL}"
echo "This is non safe mount if something already exists will try to unmount and remount or use qs_safe_mount.sh"
echo "Usage: $0 [source_dir] [target_dir] [node_dir]"
# Step 2: Bindfs mount node directories
if [ "$NODE_DIR" == "ALL" ]; then
    echo "Mounting all node directories from $SOURCE_DIR to $TARGET_DIR..."
    for dir in "$SOURCE_DIR"/*; do
        if [ -d "$dir" ]; then  # Check if it is a directory
            node_name=$(basename "$dir")
            target_node_dir="$TARGET_DIR/$node_name"
            # Ensure the target directory exists
            mkdir -p "$target_node_dir"
            #check if the target directory is  mounted already if so unmount it
            grep "fuse" /proc/mounts | awk -v target="$target_node_dir" '$2 ~ "^"target {print $2}' | while read -r mount_point; do
                echo "Found existing mout point $mount_point\
                    "
               sudo fusermount -u "$mount_point"
            done
            # Mount using bindfs
            echo "Mounting $dir to $target_node_dir..."
            sudo bindfs --perms=a+w "$dir" "$target_node_dir"
        else
            echo "Skipping non-directory: $dir"
        fi
    done
    exit 0
else
    echo "Mounting node directory $NODE_DIR from $SOURCE_DIR to $TARGET_DIR..."
    source_node_dir="$SOURCE_DIR/$NODE_DIR"
    target_node_dir="$TARGET_DIR/$NODE_DIR"
    
    # Check if the source is a directory
    if [ -d "$source_node_dir" ]; then
        # Ensure the target directory exists
        mkdir -p "$target_node_dir"
        #check if the target directory is  mounted already if so unmount it
        grep "fuse" /proc/mounts | awk -v target="$target_node_dir" '$2 ~ "^"target {print $2}' | while read -r mount_point; do
           sudo fusermount -u "$mount_point"
	    echo "unmounted"
        done
            
        # Mount using bindfs
        echo "Mounting $source_node_dir to $target_node_dir..."
        sudo bindfs --perms=a+w "$source_node_dir" "$target_node_dir"
    
       exit 0
    else
        echo "Error: Source directory $source_node_dir does not exist or is not a directory!"
        exit 1
    fi
fi

echo "Operation completed!"
