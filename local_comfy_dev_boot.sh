#!/bin/bash
echo "comfy start at boot after safe mounting custom_nodes"
base_dir=$(dirname "$0")
echo "running in $base_dir"
$base_dir/qs_safe_mount.sh $base_dir/qs_custom_nodes $base_dir/custom_nodes ALL
cd $base_dir
./mount_other_dirs.sh
#now start comfy
eval "$(conda shell.bash hook)" && conda deactivate  && conda activate qs
echo "starting developer comfy server from $base_dir  Logs will come to /tmp/comfy_boot_28893.out base port 28893 nginx port 18893" 
nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
               --use-pytorch-cross-attention --port 28893 2>&1 >/tmp/comfy_28893.out &

disown

echo "comfy started 3 processes"

