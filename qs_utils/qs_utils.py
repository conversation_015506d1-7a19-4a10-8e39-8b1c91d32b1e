def mask_to_mask_latents(mask,temp=16, channels=4,reduction_size=8):
    #input B,C,H,W 
  #  motion_masks = torch.randn(16, 4, 1, 576, 1024)  # Replace this with your actual tensor T C B H W
    H=motion_masks.shape[-2]
    W=motion_masks.shape[-1]
    T=motion_masks.shape[0]
    C=motion_masks.shape[1]
    
    motion_masks = (motion_masks > 0.5).float()  # Assuming it's a binary tensor with values 0 or 1
    # First, we use F.unfold to extract 8x8 patches
    unfolded = F.unfold(motion_masks.view(-1, 1, H, W), kernel_size=8, stride=8)
    # Calculate the sum of 1s in each 8x8 patch
    patch_sums = unfolded.sum(dim=1)
    # Determine if the sum is greater than half the number of elements in the patch (i.e., 32)
    downscaled = (patch_sums > patch_size*4).float()
    downscaled = downscaled.view(T, C, 1, H // 8, W // 8)
    if channels != C :  # need to multiply on channel dimention
        channels_multi = channels//C
        # from downscaled (T,C,1, H//8, H//8) to (T,C)
    return downscaled
    